# Steering文档索引

## 📚 文档职责分工

### 🎯 [project-overview.md](./project-overview.md)
**项目全盘概览** - 项目的整体介绍和价值定位
- 项目定位和核心价值主张
- 完整的项目规模统计
- 技术架构概述
- 项目现状与展望

### 📱 [product.md](./product.md) 
**产品功能规格** - 具体的产品功能和设备规格
- 设备功能详细描述
- 技术参数和规格
- 业务模式和市场定位
- 用户体验特色

### 🏗️ [structure.md](./structure.md)
**项目结构组织** - 文件和目录的组织方式
- 完整的目录结构
- 文件命名约定
- 模块化架构
- 版本管理策略

### 🔧 [tech.md](./tech.md)
**技术栈和构建** - 技术实现和开发工具
- 核心技术栈
- 开发约束和限制
- 构建和部署流程
- 质量保证标准

### 🎨 [design-guidelines.md](./design-guidelines.md)
**设计规范指南** - 详细的UI/UX设计标准
- iOS设计理念
- 完整的视觉规范
- 组件设计标准
- 开发实现规范

## 🔗 文档关系

```
project-overview.md (总览)
├── product.md (产品功能)
├── structure.md (项目结构)
├── tech.md (技术实现)
└── design-guidelines.md (设计规范)
```

## 📖 使用指南

### 新团队成员
1. 先读 `project-overview.md` 了解项目全貌
2. 再读 `product.md` 了解产品功能
3. 查看 `structure.md` 熟悉项目结构

### 开发人员
1. 重点关注 `tech.md` 的技术约束
2. 参考 `design-guidelines.md` 的实现规范
3. 使用 `structure.md` 定位文件

### 设计师
1. 主要参考 `design-guidelines.md`
2. 结合 `product.md` 了解功能需求
3. 参考 `project-overview.md` 理解设计目标

---

*最后更新: 2025-07-27*
*合觅App项目 - Steering文档体系*