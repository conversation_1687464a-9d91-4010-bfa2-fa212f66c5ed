# 合觅App UE设计规范与开发指南

## 📱 设计理念

### 核心原则
- **iOS原生体验**: 严格遵循苹果设计规范，提供原生级别的用户体验
- **静态原型**: 纯HTML/CSS实现，无JavaScript交互，专注于UX设计验证
- **一致性优先**: 所有49个页面保持统一的视觉和交互模式
- **中文本土化**: 针对中国市场的本土化设计和功能适配

### 技术约束
- **严格禁止JavaScript**: 所有页面必须是纯静态HTML+CSS
- **无交互链接**: 使用`<div>`替代`<a>`标签，避免实际跳转
- **状态展示**: 通过静态HTML结构展示不同的界面状态
- **响应式设计**: 主要适配iPhone (393px × 852px)，支持多屏幕尺寸

## 🎨 视觉设计规范

### 颜色系统
```css
/* 主色调 */
--primary-blue: #3b82f6;
--deep-blue: #1d4ed8;
--light-blue: #60a5fa;

/* 背景渐变 */
--page-gradient: linear-gradient(180deg, #e6f3ff 0%, #f0f8ff 50%, #e8f4f8 100%);
--status-bar-gradient: linear-gradient(180deg, #e6f3ff 0%, #f0f8ff 100%);

/* 玻璃效果 */
--glass-background: rgba(255, 255, 255, 0.9);
--glass-blur: backdrop-filter: blur(10px);
```

### 字体规范
- **大标题**: `text-2xl` (24px) - 页面主标题
- **导航标题**: `text-lg` (18px) - 导航栏标题
- **卡片标题**: `text-base` (16px) - 功能卡片标题
- **正文**: `text-sm` (14px) - 主要内容文字
- **辅助文字**: `text-xs` (12px) - 说明和提示文字

### 间距系统
- **页面边距**: `px-4` (16px) - 页面左右边距
- **顶部间距**: `pt-24` (96px) - 适配iOS状态栏和导航栏
- **底部间距**: `pb-8` (32px) - 与iOS底部指示器的安全距离
- **组件间距**: `space-y-3` (12px) - 列表项之间的标准间距

## 📐 布局架构

### 页面层级结构
```html
<!-- iOS状态栏 (z-50, top-0) -->
<div class="fixed top-0 left-0 right-0 ... z-50">
    <div class="font-semibold">9:41</div>
    <!-- 信号、WiFi、电池图标 -->
</div>

<!-- 顶部导航栏 (z-40, top-10) -->
<div class="fixed top-10 left-0 right-0 ... z-40">
    <!-- 返回按钮、标题、更多按钮 -->
</div>

<!-- 告警横条 (z-30, top-20) [可选] -->
<div class="fixed top-20 left-0 right-0 z-30">
    <!-- 低电量等告警信息 -->
</div>

<!-- 主内容区域 -->
<div class="pt-24 pb-8 px-4">
    <!-- 页面主要内容 -->
    
    <!-- 底部操作按钮 -->
    <div class="mt-12">
        <div class="w-full bg-blue-500 text-white py-4 rounded-xl">
            按钮文字
        </div>
    </div>
</div>

<!-- iOS底部指示器 (bottom-0) -->
<div class="fixed bottom-0 left-0 right-0 flex justify-center pt-4 pb-2">
    <div class="w-32 h-1 bg-black rounded-full"></div>
</div>
```

### 关键布局规则
1. **主容器完整性**: 所有内容必须在`pt-24 pb-8 px-4`容器内
2. **按钮定位**: 底部按钮使用`mt-12`间距，位于主容器内部
3. **HTML结构完整**: 确保所有标签正确配对，无遗漏或多余的关闭标签
4. **垂直对齐一致性**: 不同状态页面保持相同的垂直对齐

## 🧩 组件规范

### 设备卡片组件
```html
<div class="device-item rounded-xl p-3 mb-3" 
     style="background: rgba(255, 255, 255, 0.9); backdrop-filter: blur(10px);">
    <div class="flex items-center">
        <div class="w-8 h-8 rounded-lg mr-3 flex items-center justify-center"
             style="background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);">
            <i class="fas fa-lightbulb text-white text-sm"></i>
        </div>
        <div class="flex-1">
            <div class="font-medium text-gray-800">设备名称</div>
            <div class="text-xs text-gray-500">设备状态</div>
        </div>
        <div class="text-right">
            <!-- 开关或状态指示器 -->
        </div>
    </div>
</div>
```

### 开关控件
```html
<!-- 开启状态 -->
<div class="relative inline-block w-12 h-6">
    <div class="block w-full h-full bg-blue-500 rounded-full">
        <span class="absolute left-1 top-1 w-4 h-4 bg-white rounded-full transform translate-x-6"></span>
    </div>
</div>

<!-- 关闭状态 -->
<div class="relative inline-block w-12 h-6">
    <div class="block w-full h-full bg-gray-300 rounded-full">
        <span class="absolute left-1 top-1 w-4 h-4 bg-white rounded-full"></span>
    </div>
</div>
```

### 功能分组
```html
<!-- 功能设置区域 -->
<div class="mx-4 mt-4">
    <div class="mb-3">
        <h3 class="text-sm font-medium text-gray-500 px-2">功能设置</h3>
    </div>
    <div class="space-y-1">
        <!-- 功能相关设置项 -->
    </div>
</div>

<!-- 通用设置区域 -->
<div class="mx-4 mt-8">
    <div class="mb-3">
        <h3 class="text-sm font-medium text-gray-500 px-2">通用设置</h3>
    </div>
    <div class="space-y-1">
        <!-- 通用管理设置项 -->
    </div>
</div>
```

## 🔧 开发规范

### 文件命名约定
- **数字编号**: 使用两位数字编号 (01-55.html)
- **全局唯一**: 确保整个UE目录中文件名不重复
- **模块分组**: 按功能模块组织到子目录中
- **索引页面**: 每个模块包含sub-index.html导航页

### HTML结构要求
1. **DOCTYPE声明**: `<!DOCTYPE html>`
2. **语言设置**: `<html lang="zh-CN">`
3. **字符编码**: `<meta charset="UTF-8">`
4. **视口设置**: `<meta name="viewport" content="width=device-width, initial-scale=1.0">`
5. **CDN依赖**: TailwindCSS v2.2.19 + Font Awesome 6.4.0

### CSS样式规范
- **TailwindCSS优先**: 主要使用Tailwind工具类
- **自定义样式**: 复杂组件使用`<style>`块补充
- **响应式设计**: 使用Tailwind响应式前缀
- **玻璃效果**: 统一使用backdrop-filter实现

## 📱 设备特定规范

### 智能旋钮 (页面25-45)
- **疾速模式**: 简化配置流程，只支持单击操作
- **标准模式**: 完整功能，支持单击/双击/长按/旋转
- **按键配置**: 上下左右四个方向键 + 中央旋钮
- **场景触发**: 直接绑定智能场景执行

### 人体存在平板灯 (页面46-55)
- **雷达感应**: 24GHz频率，0-6米可调距离
- **亮度控制**: 1%-100%无极调节
- **情景模式**: 明亮(100%)、月光(1%)、阅读(80%)、电脑(50%)
- **自动感应**: 光照阈值可调(0-1000Lux)

### 5G CPE网关 (页面06-24)
- **网络管理**: WiFi配置、Mesh组网、速度测试
- **设备发现**: 自动发现和配对子设备
- **流量监控**: 实时流量统计和设备管理
- **固件升级**: OTA升级和系统维护

## 🎯 质量标准

### 设计一致性检查
- [ ] iOS状态栏样式统一
- [ ] 导航栏布局标准(左返回、中标题、右更多)
- [ ] 主内容区间距为pt-24 pb-8 px-4
- [ ] 底部按钮位于主容器内，使用mt-12间距
- [ ] 所有开关控件使用静态HTML展示状态

### 技术规范检查
- [ ] 页面完全静态，无JavaScript代码
- [ ] HTML结构完整，标签正确配对
- [ ] 使用div替代a标签，避免跳转
- [ ] 响应式设计适配多屏幕尺寸
- [ ] CDN依赖正确引入

### 用户体验检查
- [ ] 页面内容适配一屏显示
- [ ] 功能分组清晰(功能设置 vs 通用设置)
- [ ] 操作提示使用蓝色灯泡图标
- [ ] 术语统一(疾速模式、顺时针旋转等)
- [ ] 中文本土化表达自然

## 📚 参考页面

### 标准模板
- **结构参考**: UE/home/<USER>/27.html (标准HTML结构)
- **导航规范**: UE/home/<USER>/40.html (导航栏标准)
- **一屏布局**: UE/home/<USER>/34.html (内容适配)
- **功能分组**: UE/home/<USER>/55.html (设置分组)

### 组件示例
- **设备卡片**: UE/home/<USER>/35.html (操作配置)
- **开关控件**: UE/home/<USER>/50.html (静态开关)
- **告警横条**: UE/home/<USER>/55.html (低电量提醒)
- **底部按钮**: UE/home/<USER>/41.html (删除设备)

---

*最后更新: 2025-07-27*
*版本: v1.0 - 综合设计规范*