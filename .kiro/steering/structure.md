# 项目结构与组织

## 根目录结构
```
Homie/
├── UE/                          # Main prototype directory (current version)
├── UE copy/                     # Backup iteration 1
├── UE copy 2/                   # Backup iteration 2
├── 参考资料/                     # Reference materials (Chinese)
├── 备份/                        # Historical backups (Chinese)
├── gemini.md                    # AI documentation
├── Homie.iml                    # IDE project file
└── .kiro/                       # Kiro configuration
```

## 主要UE目录结构（已更新）
```
UE/
├── index.html                   # Master overview page with iframe grid
├── home/                        # Home & device management module
│   ├── sub-index.html          # Module navigation page
│   ├── 1.html                  # Main home dashboard
│   ├── gateway/                # 5G CPE gateway functionality
│   │   ├── 2.html              # Gateway main page
│   │   ├── 3.html              # Network settings
│   │   ├── 4-19.html           # Gateway configuration pages
│   ├── knob/                   # Smart knob configuration
│   │   ├── 20.html             # Device selection
│   │   ├── 21-29.html          # Setup and pairing
│   │   ├── 30-38.html          # Operation configuration
│   └── light/                  # Presence light settings
│       ├── 39.html             # Light control main
│       ├── 40.html             # Power-on state settings
│       ├── 41-43.html          # Advanced light settings
├── scenes/                      # Scene automation module
│   └── 49.html                 # Scene management
├── ai-assistant/                # AI assistant module
│   └── 44.html                 # AI chat interface
├── mall/                        # E-commerce module
│   └── 45.html                 # Product catalog
└── profile/                     # User profile module
    └── 47.html                 # User settings
```

## 数字文件组织系统
项目对所有49个页面使用系统化的数字命名约定：

### 首页模块 (01-43)
- **01.html**: 主要首页仪表板
- **网关页面 (02-19)**: 5G CPE网关管理（18页）
  - 网络配置、WiFi设置、设备管理
  - 速度测试、流量监控、Mesh拓扑
  - 固件更新、设备发现、家长控制
- **旋钮页面 (20-38)**: 智能旋钮设置和配置（19页）
  - 设备配对、操作配置、场景触发
  - 多功能按键映射、旋转控制
  - 疾速模式 vs 标准模式配置
- **灯光页面 (39-43)**: 存在感应灯控制（5页）
  - 自动感应设置、亮度控制、上电状态
  - PIR传感器配置、定时功能

### 其他模块 (44-49)
- **44.html**: AI助手界面
- **45.html**: 商城/电商
- **47.html**: 用户中心
- **49.html**: 场景管理

### 当前文件状态 (2025-07-27)
**注意**: 文件编号系统目前正在过渡中。一些页面可能仍使用旧编号（1.html、44.html等），而新的全局编号系统（01-55.html）正在实施中。

## 导航架构
- **主索引**: `UE/index.html`提供响应式网格的iframe概览
- **模块索引**: 每个模块都有`sub-index.html`用于详细导航
- **页面层次**: 遵循iOS导航模式，具有一致的返回按钮
- **跨模块链接**: 模块间最小链接以保持静态特性

## 设备特定组织

### 网关模块 (2-19.html)
- 网络拓扑和Mesh管理
- WiFi配置和速度测试
- 设备发现和固件更新
- 流量监控和家长控制

### 旋钮模块 (20-38.html)
- 设备选择和配对工作流程
- 操作配置（单击/双击/长按）
- 场景触发设置和设备控制映射
- 高级设置和自定义

### 灯光模块 (39-43.html)
- 存在检测设置和灵敏度
- 带亮度调节的自动感应灯光控制（1%-100%）
- 上电状态配置（记忆/默认开/默认关）
- 定时功能和智能场景集成

## 备份与版本管理
```
备份/                            # Backup directory
├── UE0706/                     # July 6th backup
├── UE0707/                     # July 7th backup
├── UE0708/                     # July 8th backup
├── UE0709/                     # July 9th backup
├── UE0710/                     # July 10th backup
├── UE0712/                     # July 12th backup
├── UE0713/                     # July 13th backup
├── UE0714/                     # July 14th backup
└── UE0715/                     # July 15th backup
```

## 参考资料结构
```
参考资料/                        # Reference materials (Chinese)
└── 功能需求/                    # Functional requirements
    ├── 产品清单.md              # Product catalog
    ├── 人体存在平板灯功能需求.md   # Presence light requirements
    └── 旋钮开关功能需求.md        # Smart knob requirements
```

## 文件命名约定
- **数字系统**: 逻辑流程的顺序编号（1-49.html）
- **模块分组**: 相关功能按数字范围分组
- **描述性目录**: 清晰的文件夹名称（gateway、knob、light）
- **一致结构**: 所有页面遵循相同的HTML结构和样式

## 组件架构
- **通用UI元素**: 在所有49个页面中标准化（参见`design-guidelines.md`）
- **设备特定组件**: 网关、旋钮和灯光模块的专用界面
- **模块化设计**: 具有一致样式模式的可重用组件

## 开发工作流程
1. **主要开发**: 在`UE/`目录中使用数字文件系统工作
2. **更改前备份**: 复制到按日期命名的备份文件夹
3. **模块开发**: 使用有组织的目录结构
4. **设计验证**: 所有页面的一致iOS设计模式
5. **跨模块测试**: 使用主`index.html`进行概览

## 关键组织原则
- **数字流程**: 逻辑用户旅程的顺序页面编号
- **模块化架构**: 按设备类型和功能清晰分离
- **静态导航**: 无动态路由，明确的页面关系
- **版本控制**: 基于日期命名的手动备份系统
- **设计一致性**: 所有49个页面的统一iOS设计模式
- **双语支持**: 中文UI配合英文代码结构