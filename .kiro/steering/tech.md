# 技术栈与构建系统

## 核心技术
- **HTML5**: 所有UE原型页面的语义化标记
- **CSS3**: 自定义样式，包含渐变、背景滤镜和响应式设计
- **TailwindCSS v2.2.19**: 通过CDN引入的工具优先CSS框架
- **Font Awesome 6.4.0**: 通过CDN引入的图标库
- **静态文件**: 无需构建过程，直接文件服务

## 开发约束
- **禁用JavaScript**: 所有页面必须是纯静态HTML/CSS
- **无交互链接**: 使用`<div>`替代`<a>`标签作为按钮
- **无悬停效果**: 避免CSS过渡、变换或悬停状态
- **静态状态展示**: 通过独立的HTML结构展示不同状态

## 样式架构
- **TailwindCSS工具类**: 主要使用工具类的样式方法
- **自定义CSS**: 复杂组件使用`<style>`块补充样式
- **iOS设计系统**: 特定的配色方案、间距和组件模式
- **背景滤镜**: 大量使用`backdrop-filter: blur()`实现玻璃效果

## 技术架构
- **静态原型**: 纯HTML/CSS实现，用于快速UX迭代
- **CDN依赖**: TailwindCSS v2.2.19 + Font Awesome 6.4.0
- **无构建过程**: 直接文件服务，无需编译
- **文件组织**: 详细目录布局参见`structure.md`
- **设计标准**: 完整规范参见`design-guidelines.md`

## 代码质量标准
- **HTML验证**: 所有标签必须正确配对和嵌套
- **静态实现**: 任何页面都不允许JavaScript代码
- **CSS架构**: TailwindCSS工具类 + 复杂组件的自定义`<style>`块
- **组件一致性**: 详细组件规范参考`design-guidelines.md`

## 开发工作流程
由于这是静态原型项目，无需构建命令：

### 查看原型
```bash
# 本地启动静态服务器
python3 -m http.server 8000
# 或者
npx serve .
# 然后打开 http://localhost:8000/UE/index.html
```

### 文件管理
```bash
# 重大更改前创建备份
cp -r UE "UE backup $(date +%m%d)"

# 按类别查看所有HTML文件
find UE/home/<USER>"*.html" | sort -V
find UE/home/<USER>"*.html" | sort -V
find UE/home/<USER>"*.html" | sort -V

# 检查重复文件名（应该返回空）
find UE -name "*.html" -not -path "*/sub-index.html" -exec basename {} \; | sort | uniq -d
```

### 设计验证
- 打开`UE/index.html`查看iframe网格的原型概览
- 每个模块都有`sub-index.html`用于详细导航
- 单个页面遵循`gemini.md`中的严格iOS设计标准
- 使用浏览器开发工具测试响应式行为
- 验证HTML结构完整性和标签正确配对

### 质量保证
- **设计一致性**: 所有页面遵循iOS设计模式
- **文件命名**: 整个UE目录中全局唯一性
- **HTML结构**: 正确的容器层次和标签配对
- **静态特性**: 任何页面都没有JavaScript代码
- **响应式设计**: 跨屏幕尺寸的正确适配

## 关键依赖 (CDN)
- TailwindCSS: `https://unpkg.com/tailwindcss@2.2.19/dist/tailwind.min.css`
- Font Awesome: `https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css`

## 浏览器兼容性
- 针对iOS Safari和基于WebKit的浏览器优化
- 使用现代CSS特性（backdrop-filter、CSS Grid、Flexbox）
- 移动优先的响应式设计
- 标准视口：393px × 852px（iPhone尺寸）