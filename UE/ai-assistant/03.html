<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI智能体</title>
    <link href="https://unpkg.com/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            background: linear-gradient(180deg, #e6f3ff 0%, #f0f8ff 50%, #e8f4f8 100%);
            overflow-x: hidden;
        }
        .device-card {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        .ai-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .feature-card {
            background: rgba(255, 255, 255, 0.95);
            border: 1px solid rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        .feature-card .bg-orange-500 {
            background-color: #f97316 !important;
        }
        .feature-card .bg-green-500 {
            background-color: #22c55e !important;
        }
        .feature-card .bg-purple-500 {
            background-color: #a855f7 !important;
        }
        .feature-card .bg-yellow-500 {
            background-color: #eab308 !important;
        }
        .voice-wave {
            opacity: 0.8;
        }
    </style>
</head>
<body class="h-screen">
    <!-- iOS状态栏 -->
    <div class="fixed top-0 left-0 right-0 text-black text-sm px-4 py-2 flex justify-between items-center z-50" style="background: linear-gradient(180deg, #e6f3ff 0%, #f0f8ff 100%);">
        <div class="font-semibold">
            9:41
        </div>
        <div class="flex items-center space-x-1">
            <i class="fas fa-signal text-xs"></i>
            <i class="fas fa-wifi text-xs"></i>
            <i class="fas fa-battery-full text-xs"></i>
        </div>
    </div>

    <!-- 主内容区域 -->
    <div class="flex-1 overflow-y-auto pt-12 pb-20">
        <!-- 顶部导航 -->
        <div class="flex justify-between items-center p-4">
            <div>
                <h1 class="text-2xl font-bold text-gray-800">AI智能体</h1>
                <p class="text-sm text-gray-600">全屋智能的AI助手</p>
            </div>
            <div class="flex space-x-4">
                <button class="p-3 bg-white bg-opacity-60 rounded-full">
                    <i class="fas fa-history text-gray-600"></i>
                </button>
                <button class="p-3 bg-white bg-opacity-60 rounded-full">
                    <i class="fas fa-cog text-gray-600"></i>
                </button>
            </div>
        </div>

        <!-- AI助手主卡片 -->
        <div class="mx-4 mb-6">
            <div class="ai-card rounded-2xl p-6 text-center">
                <div class="voice-wave w-20 h-20 bg-white bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-microphone text-3xl"></i>
                </div>
                <h2 class="text-xl font-bold mb-2">小觅助手</h2>
                <p class="text-sm opacity-90 mb-4">您的全屋智能管家，随时为您服务</p>
                <button class="bg-white bg-opacity-20 text-white px-6 py-2 rounded-full text-sm font-medium">
                    <i class="fas fa-microphone mr-2"></i>开始对话
                </button>
            </div>
        </div>

        <!-- 最近对话 -->
        <div class="mx-4 mb-6">
            <div class="flex items-center justify-between mb-3">
                <h3 class="text-lg font-semibold text-gray-800">最近对话</h3>
                <button class="text-blue-500 text-sm">查看全部</button>
            </div>
            <div class="space-y-2">
                <div class="device-card rounded-lg p-3">
                    <div class="flex items-center">
                        <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center mr-3">
                            <i class="fas fa-user text-white text-xs"></i>
                        </div>
                        <div class="flex-1">
                            <p class="text-sm text-gray-800">"小觅，把客厅灯光调暗一点"</p>
                            <p class="text-xs text-gray-500">2分钟前</p>
                        </div>
                    </div>
                </div>

                <div class="device-card rounded-lg p-3">
                    <div class="flex items-center">
                        <div class="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center mr-3">
                            <i class="fas fa-robot text-white text-xs"></i>
                        </div>
                        <div class="flex-1">
                            <p class="text-sm text-gray-800">"好的，已为您调整客厅灯光亮度"</p>
                            <p class="text-xs text-gray-500">2分钟前</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 快捷指令 -->
        <div class="mx-4 mb-6">
            <h3 class="text-lg font-semibold text-gray-800 mb-3">快捷指令</h3>
            <div class="grid grid-cols-2 gap-3">
                <div class="feature-card rounded-xl p-4">
                    <div class="w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center mb-3">
                        <i class="fas fa-home text-white"></i>
                    </div>
                    <h4 class="font-semibold text-gray-800 mb-1">回家模式</h4>
                    <p class="text-xs text-gray-600">"小觅，我回家了"</p>
                </div>

                <div class="feature-card rounded-xl p-4">
                    <div class="w-10 h-10 bg-purple-500 rounded-lg flex items-center justify-center mb-3">
                        <i class="fas fa-moon text-white"></i>
                    </div>
                    <h4 class="font-semibold text-gray-800 mb-1">睡眠模式</h4>
                    <p class="text-xs text-gray-600">"小觅，晚安"</p>
                </div>

                <div class="feature-card rounded-xl p-4">
                    <div class="w-10 h-10 bg-green-500 rounded-lg flex items-center justify-center mb-3">
                        <i class="fas fa-sun text-white"></i>
                    </div>
                    <h4 class="font-semibold text-gray-800 mb-1">起床模式</h4>
                    <p class="text-xs text-gray-600">"小觅，早上好"</p>
                </div>

                <div class="feature-card rounded-xl p-4">
                    <div class="w-10 h-10 bg-red-500 rounded-lg flex items-center justify-center mb-3">
                        <i class="fas fa-utensils text-white"></i>
                    </div>
                    <h4 class="font-semibold text-gray-800 mb-1">用餐模式</h4>
                    <p class="text-xs text-gray-600">"小觅，开始用餐"</p>
                </div>
            </div>
        </div>

        <!-- AI功能 -->
        <div class="mx-4 mb-6">
            <h3 class="text-lg font-semibold text-gray-800 mb-3">AI功能</h3>
            <div class="space-y-3">
                <div class="device-card rounded-xl p-4">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-blue-500 rounded-xl flex items-center justify-center mr-3">
                            <i class="fas fa-brain text-white text-lg"></i>
                        </div>
                        <div class="flex-1">
                            <h4 class="font-semibold text-gray-800">智能学习</h4>
                            <p class="text-sm text-gray-600">学习您的生活习惯，主动优化设备控制</p>
                        </div>
                        <i class="fas fa-chevron-right text-gray-400"></i>
                    </div>
                </div>

                <div class="device-card rounded-xl p-4">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-green-500 rounded-xl flex items-center justify-center mr-3">
                            <i class="fas fa-comments text-white text-lg"></i>
                        </div>
                        <div class="flex-1">
                            <h4 class="font-semibold text-gray-800">自然对话</h4>
                            <p class="text-sm text-gray-600">支持自然语言交互，理解复杂指令</p>
                        </div>
                        <i class="fas fa-chevron-right text-gray-400"></i>
                    </div>
                </div>

                <div class="device-card rounded-xl p-4">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-purple-500 rounded-xl flex items-center justify-center mr-3">
                            <i class="fas fa-magic text-white text-lg"></i>
                        </div>
                        <div class="flex-1">
                            <h4 class="font-semibold text-gray-800">智能推荐</h4>
                            <p class="text-sm text-gray-600">基于环境和时间，智能推荐最佳场景</p>
                        </div>
                        <i class="fas fa-chevron-right text-gray-400"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- 生活技能 -->
        <div class="mx-4 mb-6">
            <h3 class="text-lg font-semibold text-gray-800 mb-3">生活技能</h3>
            <div class="grid grid-cols-3 gap-3">
                <!-- 第一行 -->
                <div class="feature-card rounded-xl p-3 text-center">
                    <div class="w-12 h-12 bg-orange-500 rounded-full flex items-center justify-center mx-auto mb-2">
                        <i class="fas fa-bell text-white"></i>
                    </div>
                    <h4 class="text-sm font-semibold text-gray-800">闹钟</h4>
                </div>

                <div class="feature-card rounded-xl p-3 text-center">
                    <div class="w-12 h-12 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-2">
                        <i class="fas fa-phone text-white"></i>
                    </div>
                    <h4 class="text-sm font-semibold text-gray-800">通话</h4>
                </div>

                <div class="feature-card rounded-xl p-3 text-center">
                    <div class="w-12 h-12 bg-orange-500 rounded-full flex items-center justify-center mx-auto mb-2">
                        <i class="fas fa-heart text-white"></i>
                    </div>
                    <h4 class="text-sm font-semibold text-gray-800">小爱训练</h4>
                </div>

                <!-- 第二行 -->
                <div class="feature-card rounded-xl p-3 text-center">
                    <div class="w-12 h-12 bg-purple-500 rounded-full flex items-center justify-center mx-auto mb-2">
                        <i class="fas fa-moon text-white"></i>
                    </div>
                    <h4 class="text-sm font-semibold text-gray-800">睡眠模式</h4>
                </div>

                <div class="feature-card rounded-xl p-3 text-center">
                    <div class="w-12 h-12 bg-orange-500 rounded-full flex items-center justify-center mx-auto mb-2">
                        <i class="fas fa-map-marker-alt text-white"></i>
                    </div>
                    <h4 class="text-sm font-semibold text-gray-800">地震播报</h4>
                </div>

                <div class="feature-card rounded-xl p-3 text-center">
                    <div class="w-12 h-12 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-2">
                        <i class="fas fa-clock text-white"></i>
                    </div>
                    <h4 class="text-sm font-semibold text-gray-800">整点报时</h4>
                </div>
            </div>
        </div>


    </div>

    <!-- 底部导航栏 -->
    <div class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200">
        <div class="flex justify-around py-2">
            <button class="flex flex-col items-center py-2 px-3">
                <i class="fas fa-home text-gray-400 text-lg mb-1"></i>
                <span class="text-xs text-gray-400">首页</span>
            </button>
            <button class="flex flex-col items-center py-2 px-3">
                <i class="fas fa-layer-group text-gray-400 text-lg mb-1"></i>
                <span class="text-xs text-gray-400">场景</span>
            </button>
            <button class="flex flex-col items-center py-2 px-3">
                <i class="fas fa-robot text-blue-500 text-lg mb-1"></i>
                <span class="text-xs text-blue-500 font-medium">AI智能体</span>
            </button>
            <button class="flex flex-col items-center py-2 px-3">
                <i class="fas fa-store text-gray-400 text-lg mb-1"></i>
                <span class="text-xs text-gray-400">商城</span>
            </button>
            <button class="flex flex-col items-center py-2 px-3">
                <i class="fas fa-user text-gray-400 text-lg mb-1"></i>
                <span class="text-xs text-gray-400">我的</span>
            </button>
        </div>
    </div>

    <!-- iOS底部指示器 -->
    <div class="fixed bottom-0 left-0 right-0 flex justify-center pt-4 pb-2 bg-transparent">
        <div class="w-32 h-1 bg-black rounded-full"></div>
    </div>
</body>
</html>
