<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>合觅App - 首页</title>
    <link href="https://unpkg.com/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            background: linear-gradient(180deg, #e6f3ff 0%, #f0f8ff 50%, #e8f4f8 100%);
        }
        .device-card {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        .room-tag {
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(5px);
        }

    </style>
</head>
<body class="h-screen">
    <!-- iOS状态栏 -->
    <div class="fixed top-0 left-0 right-0 text-black text-sm px-4 py-2 flex justify-between items-center z-50" style="background: linear-gradient(180deg, #e6f3ff 0%, #f0f8ff 100%);">
        <div class="font-semibold">
            9:41
        </div>
        <div class="flex items-center space-x-1">
            <i class="fas fa-signal text-xs"></i>
            <i class="fas fa-wifi text-xs"></i>
            <i class="fas fa-battery-full text-xs"></i>
        </div>
    </div>

    <!-- 主内容区域 -->
    <div class="flex-1 overflow-y-auto pt-12 pb-20">
        <!-- 顶部导航 -->
        <div class="flex justify-between items-center p-4">
            <div>
                <h1 class="text-2xl font-bold text-gray-800">我家</h1>
                <p class="text-sm text-gray-600">智能生活，从这里开始</p>
            </div>
            <div class="flex space-x-4">
                <button class="p-3 bg-white bg-opacity-60 rounded-full">
                    <i class="fas fa-plus text-gray-600"></i>
                </button>
                <button class="p-3 bg-white bg-opacity-60 rounded-full">
                    <i class="fas fa-bell text-gray-600"></i>
                </button>
            </div>
        </div>

        <!-- 我的设备区域 -->
        <div class="px-4 mb-6">
            <h2 class="text-gray-800 text-lg font-semibold mb-4">我的设备</h2>

            <!-- 房间筛选标签 -->
            <div class="flex space-x-2 mb-4 overflow-x-auto">
                <button class="bg-blue-500 text-white px-4 py-2 rounded-lg text-sm font-medium whitespace-nowrap">
                    🏠 全部
                </button>
                <button class="bg-gray-100 text-gray-600 px-4 py-2 rounded-lg text-sm font-medium whitespace-nowrap">
                    🛋️ 客厅
                </button>
                <button class="bg-gray-100 text-gray-600 px-4 py-2 rounded-lg text-sm font-medium whitespace-nowrap">
                    🛏️ 卧室
                </button>
                <button class="bg-gray-100 text-gray-600 px-4 py-2 rounded-lg text-sm font-medium whitespace-nowrap">
                    🍳 厨房
                </button>
                <button class="bg-gray-100 text-gray-600 px-4 py-2 rounded-lg text-sm font-medium whitespace-nowrap">
                    🚪 门厅
                </button>
            </div>

            <!-- 智能家居设备 -->
            <div class="grid grid-cols-2 gap-3">
                <!-- 5G CPE -->
                <div class="device-card rounded-xl p-3 text-center">
                    <div class="w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center mx-auto mb-2">
                        <i class="fa-solid fa-charging-station text-white"></i>
                    </div>
                    <p class="text-sm font-medium text-gray-800">5G CPE</p>
                    <p class="text-xs text-green-500">在线 · 已实名</p>
                </div>

                <!-- 智能音箱 -->
                <div class="device-card rounded-xl p-3 text-center">
                    <div class="w-10 h-10 bg-purple-500 rounded-lg flex items-center justify-center mx-auto mb-2">
                        <i class="fas fa-music text-white"></i>
                    </div>
                    <p class="text-sm font-medium text-gray-800">智能音箱</p>
                    <p class="text-xs text-gray-500">离线</p>
                </div>

                <!-- 智能屏 -->
                <div class="device-card rounded-xl p-3 text-center">
                    <div class="w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center mx-auto mb-2">
                        <i class="fas fa-tablet text-white"></i>
                    </div>
                    <p class="text-sm font-medium text-gray-800">智能屏</p>
                    <p class="text-xs text-green-500">在线 · 播放中</p>
                </div>

                <!-- 智能开关 -->
                <div class="device-card rounded-xl p-3 text-center">
                    <div class="w-10 h-10 bg-green-500 rounded-lg flex items-center justify-center mx-auto mb-2">
                        <i class="fas fa-power-off text-white"></i>
                    </div>
                    <p class="text-sm font-medium text-gray-800">智能开关</p>
                    <p class="text-xs text-green-500">在线 · 已开启</p>
                </div>

                <!-- 人在传感器 -->
                <div class="device-card rounded-xl p-3 text-center">
                    <div class="w-10 h-10 bg-green-500 rounded-lg flex items-center justify-center mx-auto mb-2">
                        <i class="fas fa-user text-white"></i>
                    </div>
                    <p class="text-sm font-medium text-gray-800">人在传感器</p>
                    <p class="text-xs text-green-500">在线 · 有人</p>
                </div>

                <!-- 智能锁 -->
                <div class="device-card rounded-xl p-3 text-center">
                    <div class="w-10 h-10 bg-red-500 rounded-lg flex items-center justify-center mx-auto mb-2">
                        <i class="fas fa-lock text-white"></i>
                    </div>
                    <p class="text-sm font-medium text-gray-800">智能锁</p>
                    <p class="text-xs text-green-500">在线 · 已锁定</p>
                </div>

                <!-- 双键开关 -->
                <div class="device-card rounded-xl p-3 text-center">
                    <div class="w-10 h-10 bg-red-500 rounded-lg flex items-center justify-center mx-auto mb-2">
                        <i class="fa fa-toggle-on text-white"></i>
                    </div>
                    <p class="text-sm font-medium text-gray-800">双键开关</p>
                    <p class="text-xs text-green-500">在线 · 1键开启</p>
                </div>

                <!-- 温湿度传感器 -->
                <div class="device-card rounded-xl p-3 text-center">
                    <div class="w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center mx-auto mb-2">
                        <i class="fas fa-thermometer-half text-white"></i>
                    </div>
                    <p class="text-sm font-medium text-gray-800">温湿度传感器</p>
                    <p class="text-xs text-green-500">在线 · 23°C 65%</p>
                </div>

                <!-- 添加设备 -->
                <div class="device-card rounded-xl p-3 text-center border-2 border-dashed border-gray-300 cursor-pointer">
                    <div class="w-10 h-10 rounded-lg flex items-center justify-center mx-auto mb-2">
                        <i class="fas fa-plus text-gray-400"></i>
                    </div>
                    <p class="text-sm font-medium text-gray-500">添加设备</p>
                </div>
            </div>
        </div>

    </div>





    <!-- 底部导航栏 -->
    <div class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200">
        <div class="flex justify-around py-2">
            <button class="flex flex-col items-center py-2 px-3">
                <i class="fas fa-home text-blue-500 text-lg mb-1"></i>
                <span class="text-xs text-blue-500 font-medium">首页</span>
            </button>
            <button class="flex flex-col items-center py-2 px-3">
                <i class="fas fa-layer-group text-gray-400 text-lg mb-1"></i>
                <span class="text-xs text-gray-400">场景</span>
            </button>
            <button class="flex flex-col items-center py-2 px-3">
                <i class="fas fa-robot text-gray-400 text-lg mb-1"></i>
                <span class="text-xs text-gray-400">AI智能体</span>
            </button>
            <button class="flex flex-col items-center py-2 px-3">
                <i class="fas fa-store text-gray-400 text-lg mb-1"></i>
                <span class="text-xs text-gray-400">商城</span>
            </button>
            <button class="flex flex-col items-center py-2 px-3">
                <i class="fas fa-user text-gray-400 text-lg mb-1"></i>
                <span class="text-xs text-gray-400">我的</span>
            </button>
        </div>
    </div>

    <!-- iOS底部指示器 -->
    <div class="fixed bottom-0 left-0 right-0 flex justify-center pt-4 pb-2 bg-transparent">
        <div class="w-32 h-1 bg-black rounded-full"></div>
    </div>
</body>
</html>
