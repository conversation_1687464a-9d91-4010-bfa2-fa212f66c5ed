<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>首页相关页面 - 合觅App原型UE设计</title>
    <link href="https://unpkg.com/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            background-color: #f6f6f6;
        }

        .iframe-container {
            background-color: #f6f6f6;
        }

        iframe {
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            max-width: 100%;
        }

        /* 响应式iframe尺寸 */
        @media (min-width: 1024px) {
            .responsive-iframe {
                width: 393px;
                height: 852px;
            }
        }

        @media (max-width: 1023px) and (min-width: 768px) {
            .responsive-iframe {
                width: 280px;
                height: 607px;
            }
        }

        @media (max-width: 767px) {
            .responsive-iframe {
                width: 100%;
                max-width: 393px;
                height: 852px;
            }
        }
    </style>
</head>

<body class="min-h-screen p-2 md:p-6 lg:p-8">
    <div class="max-w-7xl mx-auto">
        <!-- 页面标题 -->
        <div class="text-center mb-8">
            <h1 class="text-2xl md:text-3xl lg:text-4xl font-bold text-gray-800">首页</h1>
        </div>

        <!-- 返回按钮 -->
        <div class="mb-8">
            <button onclick="window.location.href='../index.html'"
                class="flex items-center text-blue-600 hover:text-blue-800">
                <i class="fas fa-arrow-left mr-2"></i>
                <span class="text-lg font-medium">返回主页</span>
            </button>
        </div>

        <!-- 设备绑定功能 -->
        <div class="mb-12">
            <h2 class="text-3xl font-bold text-gray-800 mb-8">1. 设备绑定</h2>

            <!-- 网关设备绑定 -->
            <div class="mb-10">
                <h3 class="text-xl font-semibold text-blue-600 mb-6">1.1 网关设备</h3>
                <div
                    class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2 md:gap-6 lg:gap-8 iframe-container mb-6">
                    <!-- 添加设备 -->
                    <div class="text-center">
                        <h4 class="text-base md:text-lg font-medium text-gray-700 mb-2">添加设备</h4>
                        <iframe src="gateway/4.html" class="responsive-iframe mx-auto mb-2"></iframe>
                        <p class="text-sm text-gray-600"><span
                                class="text-blue-600 cursor-pointer hover:text-blue-800 hover:underline"
                                onclick="window.location.href='gateway/4.html'">4</span>
                        </p>
                    </div>

                    <!-- 按型号添加 -->
                    <div class="text-center">
                        <h4 class="text-base md:text-lg font-medium text-gray-700 mb-2">按型号添加</h4>
                        <iframe src="gateway/3.html" class="responsive-iframe mx-auto mb-2"></iframe>
                        <p class="text-sm text-gray-600"><span
                                class="text-blue-600 cursor-pointer hover:text-blue-800 hover:underline"
                                onclick="window.location.href='gateway/3.html'">3</span>
                        </p>
                    </div>

                    <!-- 添加网关 -->
                    <div class="text-center">
                        <h4 class="text-base md:text-lg font-medium text-gray-700 mb-2">添加网关</h4>
                        <iframe src="gateway/5.html" class="responsive-iframe mx-auto mb-2"></iframe>
                        <p class="text-sm text-gray-600"><span
                                class="text-blue-600 cursor-pointer hover:text-blue-800 hover:underline"
                                onclick="window.location.href='gateway/5.html'">5</span>
                        </p>
                    </div>
                </div>
            </div>

            <!-- 子设备绑定 -->
            <div class="mb-10">
                <h3 class="text-xl font-semibold text-blue-600 mb-6">1.2 子设备</h3>
                <div
                    class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2 md:gap-6 lg:gap-8 iframe-container mb-6">
                    <!-- 选择子设备 -->
                    <div class="text-center">
                        <h4 class="text-base md:text-lg font-medium text-gray-700 mb-2">选择子设备</h4>
                        <iframe src="knob/20.html" class="responsive-iframe mx-auto mb-2"></iframe>
                        <p class="text-sm text-gray-600"><span
                                class="text-blue-600 cursor-pointer hover:text-blue-800 hover:underline"
                                onclick="window.location.href='knob/20.html'">20</span>
                        </p>
                    </div>

                    <!-- 子设备添加-选择网关 -->
                    <div class="text-center">
                        <h4 class="text-base md:text-lg font-medium text-gray-700 mb-2">选择网关添加</h4>
                        <iframe src="knob/34.html" class="responsive-iframe mx-auto mb-2"></iframe>
                        <p class="text-sm text-gray-600"><span
                                class="text-blue-600 cursor-pointer hover:text-blue-800 hover:underline"
                                onclick="window.location.href='knob/34.html'">34</span>
                        </p>
                    </div>

                    <!-- 子设备添加-无网关 -->
                    <div class="text-center">
                        <h4 class="text-base md:text-lg font-medium text-gray-700 mb-2">无网关添加</h4>
                        <iframe src="knob/21.html" class="responsive-iframe mx-auto mb-2"></iframe>
                        <p class="text-sm text-gray-600"><span
                                class="text-blue-600 cursor-pointer hover:text-blue-800 hover:underline"
                                onclick="window.location.href='knob/21.html'">21</span>
                        </p>
                    </div>
                </div>

                <div
                    class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2 md:gap-6 lg:gap-8 iframe-container mb-6">
                    <!-- 子设备-配置向导 -->
                    <div class="text-center">
                        <h4 class="text-base md:text-lg font-medium text-gray-700 mb-2">配置向导-智能旋钮</h4>
                        <iframe src="knob/38.html" class="responsive-iframe mx-auto mb-2"></iframe>
                        <p class="text-sm text-gray-600"><span
                                class="text-blue-600 cursor-pointer hover:text-blue-800 hover:underline"
                                onclick="window.location.href='knob/38.html'">38</span>
                        </p>
                    </div>

                    <!-- 完成绑定 -->
                    <div class="text-center">
                        <h4 class="text-base md:text-lg font-medium text-gray-700 mb-2">完成绑定</h4>
                        <iframe src="knob/22.html" class="responsive-iframe mx-auto mb-2"></iframe>
                        <p class="text-sm text-gray-600"><span
                                class="text-blue-600 cursor-pointer hover:text-blue-800 hover:underline"
                                onclick="window.location.href='knob/22.html'">22</span>
                        </p>
                    </div>

                    <!-- 绑定失败 -->
                    <div class="text-center">
                        <h4 class="text-base md:text-lg font-medium text-gray-700 mb-2">绑定失败</h4>
                        <iframe src="knob/23.html" class="responsive-iframe mx-auto mb-2"></iframe>
                        <p class="text-sm text-gray-600"><span
                                class="text-blue-600 cursor-pointer hover:text-blue-800 hover:underline"
                                onclick="window.location.href='knob/23.html'">23</span>
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 设备详情页 -->
        <div class="mb-12">
            <h2 class="text-3xl font-bold text-gray-800 mb-8">2. 设备详情页</h2>

            <!-- 网关设备详情 -->
            <div class="mb-10">
                <h3 class="text-xl font-semibold text-blue-600 mb-6">2.1 网关设备</h3>
                <div
                    class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2 md:gap-6 lg:gap-8 iframe-container mb-6">
                    <!-- 5G CPE -->
                    <div class="text-center">
                        <h4 class="text-lg font-medium text-gray-700 mb-2">5G CPE</h4>
                        <iframe src="gateway/2.html" class="responsive-iframe mx-auto mb-2"></iframe>
                        <p class="text-sm text-gray-600"><span
                                class="text-blue-600 cursor-pointer hover:text-blue-800 hover:underline"
                                onclick="window.location.href='gateway/2.html'">2</span>
                        </p>
                    </div>

                    <!-- 更多设置 -->
                    <div class="text-center">
                        <h4 class="text-lg font-medium text-gray-700 mb-2">更多设置</h4>
                        <iframe src="gateway/8.html" class="responsive-iframe mx-auto mb-2"></iframe>
                        <p class="text-sm text-gray-600"><span
                                class="text-blue-600 cursor-pointer hover:text-blue-800 hover:underline"
                                onclick="window.location.href='gateway/8.html'">8</span>
                        </p>
                    </div>

                    <!-- 固件升级 -->
                    <div class="text-center">
                        <h4 class="text-lg font-medium text-gray-700 mb-2">固件升级</h4>
                        <iframe src="gateway/11.html" class="responsive-iframe mx-auto mb-2"></iframe>
                        <p class="text-sm text-gray-600"><span
                                class="text-blue-600 cursor-pointer hover:text-blue-800 hover:underline"
                                onclick="window.location.href='gateway/11.html'">11</span>
                        </p>
                    </div>
                </div>

                <!-- 第二行：网络测速功能 -->
                <div
                    class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2 md:gap-6 lg:gap-8 iframe-container mb-6">
                    <!-- 一键测速 -->
                    <div class="text-center">
                        <h4 class="text-base md:text-lg font-medium text-gray-700 mb-2">一键测速</h4>
                        <iframe src="gateway/13.html" class="responsive-iframe mx-auto mb-2"></iframe>
                        <p class="text-sm text-gray-600"><span
                                class="text-blue-600 cursor-pointer hover:text-blue-800 hover:underline"
                                onclick="window.location.href='gateway/13.html'">13</span>
                        </p>
                    </div>

                    <!-- 测速进行中 -->
                    <div class="text-center">
                        <h4 class="text-base md:text-lg font-medium text-gray-700 mb-2">测速进行中</h4>
                        <iframe src="gateway/16.html" class="responsive-iframe mx-auto mb-2"></iframe>
                        <p class="text-sm text-gray-600"><span
                                class="text-blue-600 cursor-pointer hover:text-blue-800 hover:underline"
                                onclick="window.location.href='gateway/16.html'">16</span>
                        </p>
                    </div>

                    <!-- 测速结果 -->
                    <div class="text-center">
                        <h4 class="text-base md:text-lg font-medium text-gray-700 mb-2">测速结果</h4>
                        <iframe src="gateway/15.html" class="responsive-iframe mx-auto mb-2"></iframe>
                        <p class="text-sm text-gray-600"><span
                                class="text-blue-600 cursor-pointer hover:text-blue-800 hover:underline"
                                onclick="window.location.href='gateway/15.html'">15</span>
                        </p>
                    </div>
                </div>

                <!-- 第三行：流量清单、占位、占位 -->
                <div
                    class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2 md:gap-6 lg:gap-8 iframe-container mb-6">
                    <!-- 流量清单 -->
                    <div class="text-center">
                        <h4 class="text-lg font-medium text-gray-700 mb-2">流量清单</h4>
                        <iframe src="gateway/18.html" class="responsive-iframe mx-auto mb-2"></iframe>
                        <p class="text-sm text-gray-600"><span
                                class="text-blue-600 cursor-pointer hover:text-blue-800 hover:underline"
                                onclick="window.location.href='gateway/18.html'">18</span>
                        </p>
                    </div>

                    <!-- 占位 -->
                    <div class="text-center">
                        <h4 class="text-lg font-medium text-gray-700 mb-2 opacity-0">占位</h4>
                        <div class="w-400 h-850 mx-auto"></div>
                    </div>

                    <!-- 占位 -->
                    <div class="text-center">
                        <h4 class="text-lg font-medium text-gray-700 mb-2 opacity-0">占位</h4>
                        <div class="w-400 h-850 mx-auto"></div>
                    </div>
                </div>

                <!-- 第三行：路由器管理、Wi-Fi管理、连接管理 -->
                <div
                    class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2 md:gap-6 lg:gap-8 iframe-container mb-6">
                    <!-- 路由器管理 -->
                    <div class="text-center">
                        <h4 class="text-lg font-medium text-gray-700 mb-2">路由器管理</h4>
                        <iframe src="gateway/14.html" class="responsive-iframe mx-auto mb-2"></iframe>
                        <p class="text-sm text-gray-600"><span
                                class="text-blue-600 cursor-pointer hover:text-blue-800 hover:underline"
                                onclick="window.location.href='gateway/14.html'">14</span>
                        </p>
                    </div>

                    <!-- Wi-Fi管理 -->
                    <div class="text-center">
                        <h4 class="text-lg font-medium text-gray-700 mb-2">Wi-Fi管理</h4>
                        <iframe src="gateway/19.html" class="responsive-iframe mx-auto mb-2"></iframe>
                        <p class="text-sm text-gray-600"><span
                                class="text-blue-600 cursor-pointer hover:text-blue-800 hover:underline"
                                onclick="window.location.href='gateway/19.html'">19</span>
                        </p>
                    </div>

                    <!-- 连接管理 -->
                    <div class="text-center">
                        <h4 class="text-lg font-medium text-gray-700 mb-2">连接管理</h4>
                        <iframe src="gateway/7.html" class="responsive-iframe mx-auto mb-2"></iframe>
                        <p class="text-sm text-gray-600"><span
                                class="text-blue-600 cursor-pointer hover:text-blue-800 hover:underline"
                                onclick="window.location.href='gateway/7.html'">7</span>
                        </p>
                    </div>
                </div>

                <!-- 第四行：Mesh组网、对话记录、占位 -->
                <div
                    class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2 md:gap-6 lg:gap-8 iframe-container mb-6">
                    <!-- Mesh组网 -->
                    <div class="text-center">
                        <h4 class="text-lg font-medium text-gray-700 mb-2">Mesh组网</h4>
                        <iframe src="gateway/12.html" class="responsive-iframe mx-auto mb-2"></iframe>
                        <p class="text-sm text-gray-600"><span
                                class="text-blue-600 cursor-pointer hover:text-blue-800 hover:underline"
                                onclick="window.location.href='gateway/12.html'">12</span>
                        </p>
                    </div>

                    <!-- 对话记录 -->
                    <div class="text-center">
                        <h4 class="text-lg font-medium text-gray-700 mb-2">对话记录</h4>
                        <iframe src="gateway/6.html" class="responsive-iframe mx-auto mb-2"></iframe>
                        <p class="text-sm text-gray-600"><span
                                class="text-blue-600 cursor-pointer hover:text-blue-800 hover:underline"
                                onclick="window.location.href='gateway/6.html'">6</span>
                        </p>
                    </div>

                    <!-- 占位 -->
                    <div class="text-center">
                        <h4 class="text-lg font-medium text-gray-700 mb-2 opacity-0">占位</h4>
                        <div class="w-400 h-850 mx-auto"></div>
                    </div>
                </div>

                <!-- 第五行：子设备管理、发现子设备、发现设备-空状态 -->
                <div
                    class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2 md:gap-6 lg:gap-8 iframe-container mb-6">
                    <!-- 子设备管理 -->
                    <div class="text-center">
                        <h4 class="text-lg font-medium text-gray-700 mb-2">子设备管理</h4>
                        <iframe src="gateway/17.html" class="responsive-iframe mx-auto mb-2"></iframe>
                        <p class="text-sm text-gray-600"><span
                                class="text-blue-600 cursor-pointer hover:text-blue-800 hover:underline"
                                onclick="window.location.href='gateway/17.html'">17</span>
                        </p>
                    </div>

                    <!-- 发现子设备 -->
                    <div class="text-center">
                        <h4 class="text-lg font-medium text-gray-700 mb-2">发现子设备</h4>
                        <iframe src="gateway/10.html" class="responsive-iframe mx-auto mb-2"></iframe>
                        <p class="text-sm text-gray-600"><span
                                class="text-blue-600 cursor-pointer hover:text-blue-800 hover:underline"
                                onclick="window.location.href='gateway/10.html'">10</span>
                        </p>
                    </div>

                    <!-- 发现设备-空状态 -->
                    <div class="text-center">
                        <h4 class="text-lg font-medium text-gray-700 mb-2">发现子设备-空状态</h4>
                        <iframe src="gateway/9.html" class="responsive-iframe mx-auto mb-2"></iframe>
                        <p class="text-sm text-gray-600"><span
                                class="text-blue-600 cursor-pointer hover:text-blue-800 hover:underline"
                                onclick="window.location.href='gateway/9.html'">9</span>
                        </p>
                    </div>
                </div>
            </div>

            <!-- 智能开关详情 -->
            <div class="mb-10">
                <h3 class="text-xl font-semibold text-blue-600 mb-6">2.2 智能旋钮</h3>

                <!-- 智能旋钮 -->
                <div
                    class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2 md:gap-6 lg:gap-8 iframe-container mb-6">
                    <!-- 智能旋钮 -->
                    <div class="text-center">
                        <h4 class="text-lg font-medium text-gray-700 mb-2">智能旋钮</h4>
                        <iframe src="knob/35.html" class="responsive-iframe mx-auto mb-2"></iframe>
                        <p class="text-sm text-gray-600"><span
                                class="text-blue-600 cursor-pointer hover:text-blue-800 hover:underline"
                                onclick="window.location.href='knob/35.html'">35</span>
                        </p>
                    </div>

                    <!-- 更多设置 -->
                    <div class="text-center">
                        <h4 class="text-lg font-medium text-gray-700 mb-2">更多设置</h4>
                        <iframe src="knob/37.html" class="responsive-iframe mx-auto mb-2"></iframe>
                        <p class="text-sm text-gray-600"><span
                                class="text-blue-600 cursor-pointer hover:text-blue-800 hover:underline"
                                onclick="window.location.href='knob/37.html'">37</span>
                        </p>
                    </div>

                </div>

                <div
                    class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2 md:gap-6 lg:gap-8 iframe-container mb-6">
                    <!-- 按键配置 -->
                    <div class="text-center">
                        <h4 class="text-lg font-medium text-gray-700 mb-2">选择按键</h4>
                        <iframe src="knob/29.html" class="responsive-iframe mx-auto mb-2"></iframe>
                        <p class="text-sm text-gray-600"><span
                                class="text-blue-600 cursor-pointer hover:text-blue-800 hover:underline"
                                onclick="window.location.href='knob/29.html'">29</span>
                        </p>
                    </div>

                    <!-- 选择按键（极速模式） -->
                    <div class="text-center">
                        <h4 class="text-lg font-medium text-gray-700 mb-2">选择按键（疾速模式开启）</h4>
                        <iframe src="knob/28.html" class="responsive-iframe mx-auto mb-2"></iframe>
                        <p class="text-sm text-gray-600"><span
                                class="text-blue-600 cursor-pointer hover:text-blue-800 hover:underline"
                                onclick="window.location.href='knob/28.html'">28</span>
                        </p>
                    </div>

                    <!-- 选择按键 -->
                    <div class="text-center">
                        <h4 class="text-lg font-medium text-gray-700 mb-2">选择按键</h4>
                        <iframe src="knob/51.html" class="responsive-iframe mx-auto mb-2"></iframe>
                        <p class="text-sm text-gray-600"><span
                                class="text-blue-600 cursor-pointer hover:text-blue-800 hover:underline"
                                onclick="window.location.href='knob/51.html'">51</span>
                        </p>
                    </div>

                    <!-- 手动场景选择 -->
                    <div class="text-center">
                        <h4 class="text-lg font-medium text-gray-700 mb-2">选择场景</h4>
                        <iframe src="knob/33.html" class="responsive-iframe mx-auto mb-2"></iframe>
                        <p class="text-sm text-gray-600"><span
                                class="text-blue-600 cursor-pointer hover:text-blue-800 hover:underline"
                                onclick="window.location.href='knob/33.html'">33</span>
                        </p>
                    </div>

                    <!-- 旋钮操作配置 -->
                    <div class="text-center">
                        <h4 class="text-lg font-medium text-gray-700 mb-2">选择设备</h4>
                        <iframe src="knob/30.html" class="responsive-iframe mx-auto mb-2"></iframe>
                        <p class="text-sm text-gray-600"><span
                                class="text-blue-600 cursor-pointer hover:text-blue-800 hover:underline"
                                onclick="window.location.href='knob/30.html'">30</span>
                        </p>
                    </div>

                    <!-- 极简配置结果 -->
                    <div class="text-center">
                        <h4 class="text-lg font-medium text-gray-700 mb-2">配置结果</h4>
                        <iframe src="knob/31.html" class="responsive-iframe mx-auto mb-2"></iframe>
                        <p class="text-sm text-gray-600"><span
                                class="text-blue-600 cursor-pointer hover:text-blue-800 hover:underline"
                                onclick="window.location.href='knob/31.html'">31</span>
                        </p>
                    </div>
                </div>

                <div
                    class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2 md:gap-6 lg:gap-8 iframe-container mb-6">

                    <!-- 配置结果（疾速模式开启） -->
                    <div class="text-center">
                        <h4 class="text-lg font-medium text-gray-700 mb-2">配置结果（疾速模式开启）</h4>
                        <iframe src="knob/50.html" class="responsive-iframe mx-auto mb-2"></iframe>
                        <p class="text-sm text-gray-600"><span
                                class="text-blue-600 cursor-pointer hover:text-blue-800 hover:underline"
                                onclick="window.location.href='knob/50.html'">50</span>
                        </p>
                    </div>

                    <!-- 极速模式配置 -->
                    <div class="text-center">
                        <h4 class="text-lg font-medium text-gray-700 mb-2">配置结果（疾速模式开启）</h4>
                        <iframe src="knob/32.html" class="responsive-iframe mx-auto mb-2"></iframe>
                        <p class="text-sm text-gray-600"><span
                                class="text-blue-600 cursor-pointer hover:text-blue-800 hover:underline"
                                onclick="window.location.href='knob/32.html'">32</span>
                        </p>
                    </div>
                </div>

                <div
                    class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2 md:gap-6 lg:gap-8 iframe-container mb-6">
                    <!-- 按键动作配置 -->
                    <div class="text-center">
                        <h4 class="text-lg font-medium text-gray-700 mb-2">单击动作设置</h4>
                        <iframe src="knob/27.html" class="responsive-iframe mx-auto mb-2"></iframe>
                        <p class="text-sm text-gray-600"><span
                                class="text-blue-600 cursor-pointer hover:text-blue-800 hover:underline"
                                onclick="window.location.href='knob/27.html'">27</span>
                        </p>
                    </div>

                    <!-- 按键动作配置-双击展开 -->
                    <div class="text-center">
                        <h4 class="text-lg font-medium text-gray-700 mb-2">双击动作设置</h4>
                        <iframe src="knob/24.html" class="responsive-iframe mx-auto mb-2"></iframe>
                        <p class="text-sm text-gray-600"><span
                                class="text-blue-600 cursor-pointer hover:text-blue-800 hover:underline"
                                onclick="window.location.href='knob/24.html'">24</span>
                        </p>
                    </div>

                    <!-- 按键动作配置-旋转展开 -->
                    <div class="text-center">
                        <h4 class="text-lg font-medium text-gray-700 mb-2">旋转动作设置</h4>
                        <iframe src="knob/26.html" class="responsive-iframe mx-auto mb-2"></iframe>
                        <p class="text-sm text-gray-600"><span
                                class="text-blue-600 cursor-pointer hover:text-blue-800 hover:underline"
                                onclick="window.location.href='knob/26.html'">26</span>
                        </p>
                    </div>
                </div>

                <div
                    class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2 md:gap-6 lg:gap-8 iframe-container mb-6">

                    <!-- 旋转动作设置（疾速模式开启） -->
                    <div class="text-center">
                        <h4 class="text-lg font-medium text-gray-700 mb-2">旋转动作设置（疾速模式开启）</h4>
                        <iframe src="knob/25.html" class="responsive-iframe mx-auto mb-2"></iframe>
                        <p class="text-sm text-gray-600"><span
                                class="text-blue-600 cursor-pointer hover:text-blue-800 hover:underline"
                                onclick="window.location.href='knob/25.html'">25</span>
                        </p>
                    </div>
                </div>




            </div>

            <!-- 人体存在平板灯详情 -->
            <div class="mb-10">
                <h3 class="text-xl font-semibold text-blue-600 mb-6">2.3 人体存在平板灯</h3>

                <!-- 人体存在平板灯功能页面 -->
                <div
                    class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2 md:gap-6 lg:gap-8 iframe-container mb-6">
                    <!-- 设备详情 -->
                    <div class="text-center">
                        <h4 class="text-lg font-medium text-gray-700 mb-2">设备详情</h4>
                        <iframe src="light/39.html" class="responsive-iframe mx-auto mb-2"></iframe>
                        <p class="text-sm text-gray-600"><span
                                class="text-blue-600 cursor-pointer hover:text-blue-800 hover:underline"
                                onclick="window.location.href='light/39.html'">39</span>
                        </p>
                    </div>

                    <!-- 添加情景模式 -->
                    <div class="text-center">
                        <h4 class="text-lg font-medium text-gray-700 mb-2">添加情景模式</h4>
                        <iframe src="light/45.html" class="responsive-iframe mx-auto mb-2"></iframe>
                        <p class="text-sm text-gray-600"><span
                                class="text-blue-600 cursor-pointer hover:text-blue-800 hover:underline"
                                onclick="window.location.href='light/45.html'">45</span>
                        </p>
                    </div>

                    <!-- 更多设置 -->
                    <div class="text-center">
                        <h4 class="text-lg font-medium text-gray-700 mb-2">更多设置</h4>
                        <iframe src="light/41.html" class="responsive-iframe mx-auto mb-2"></iframe>
                        <p class="text-sm text-gray-600"><span
                                class="text-blue-600 cursor-pointer hover:text-blue-800 hover:underline"
                                onclick="window.location.href='light/41.html'">41</span>
                        </p>
                    </div>

                    <!-- 通电后状态 -->
                    <div class="text-center">
                        <h4 class="text-lg font-medium text-gray-700 mb-2">通电后状态</h4>
                        <iframe src="light/40.html" class="responsive-iframe mx-auto mb-2"></iframe>
                        <p class="text-sm text-gray-600"><span
                                class="text-blue-600 cursor-pointer hover:text-blue-800 hover:underline"
                                onclick="window.location.href='light/40.html'">40</span>
                        </p>
                    </div>

                    <!-- 感应设置 -->
                    <div class="text-center">
                        <h4 class="text-lg font-medium text-gray-700 mb-2">感应设置</h4>
                        <iframe src="light/43.html" class="responsive-iframe mx-auto mb-2"></iframe>
                        <p class="text-sm text-gray-600"><span
                                class="text-blue-600 cursor-pointer hover:text-blue-800 hover:underline"
                                onclick="window.location.href='light/43.html'">43</span>
                        </p>
                    </div>

                    <!-- 灯光变化设置 -->
                    <div class="text-center">
                        <h4 class="text-lg font-medium text-gray-700 mb-2">灯光变化设置</h4>
                        <iframe src="light/46.html" class="responsive-iframe mx-auto mb-2"></iframe>
                        <p class="text-sm text-gray-600"><span
                                class="text-blue-600 cursor-pointer hover:text-blue-800 hover:underline"
                                onclick="window.location.href='light/46.html'">46</span>
                        </p>
                    </div>

                    <!-- 定时开关灯 - 定时列表 -->
                    <div class="text-center">
                        <h4 class="text-lg font-medium text-gray-700 mb-2">定时开关灯 - 定时列表</h4>
                        <iframe src="light/47.html" class="responsive-iframe mx-auto mb-2"></iframe>
                        <p class="text-sm text-gray-600"><span
                                class="text-blue-600 cursor-pointer hover:text-blue-800 hover:underline"
                                onclick="window.location.href='light/47.html'">47</span>
                        </p>
                    </div>

                    <!-- 定时开关灯 - 新增定时 -->
                    <div class="text-center">
                        <h4 class="text-lg font-medium text-gray-700 mb-2">定时开关灯 - 新增定时</h4>
                        <iframe src="light/48.html" class="responsive-iframe mx-auto mb-2"></iframe>
                        <p class="text-sm text-gray-600"><span
                                class="text-blue-600 cursor-pointer hover:text-blue-800 hover:underline"
                                onclick="window.location.href='light/48.html'">48</span>
                        </p>
                    </div>
                </div>

                <!-- 新增一行用于设备日志 -->
                <div
                    class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2 md:gap-6 lg:gap-8 iframe-container mb-6">
                    <!-- 设备日志 - 雷达日志 -->
                    <div class="text-center">
                        <h4 class="text-lg font-medium text-gray-700 mb-2">设备日志 - 雷达日志</h4>
                        <iframe src="light/42.html" class="responsive-iframe mx-auto mb-2"></iframe>
                        <p class="text-sm text-gray-600"><span
                                class="text-blue-600 cursor-pointer hover:text-blue-800 hover:underline"
                                onclick="window.location.href='light/42.html'">42</span>
                        </p>
                    </div>

                    <!-- 设备日志 - 光照度日志 -->
                    <div class="text-center">
                        <h4 class="text-lg font-medium text-gray-700 mb-2">设备日志 - 光照度日志</h4>
                        <iframe src="light/44.html" class="responsive-iframe mx-auto mb-2"></iframe>
                        <p class="text-sm text-gray-600"><span
                                class="text-blue-600 cursor-pointer hover:text-blue-800 hover:underline"
                                onclick="window.location.href='light/44.html'">44</span>
                        </p>
                    </div>

                    <!-- 占位 -->
                    <div class="text-center">
                        <h4 class="text-lg font-medium text-gray-700 mb-2 opacity-0">占位</h4>
                        <div class="w-400 h-850 mx-auto"></div>
                    </div>
                </div>
            </div>

            <!-- 梦幻帘详情 -->
            <div class="mb-10">
                <h3 class="text-xl font-semibold text-blue-600 mb-6">2.4 梦幻帘</h3>

                <!-- 梦幻帘功能页面 -->
                <div
                    class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2 md:gap-6 lg:gap-8 iframe-container mb-6">
                    <!-- 设备详情 -->
                    <div class="text-center">
                        <h4 class="text-lg font-medium text-gray-700 mb-2">设备详情</h4>
                        <iframe src="curtain/56.html" class="responsive-iframe mx-auto mb-2"></iframe>
                        <p class="text-sm text-gray-600"><span
                                class="text-blue-600 cursor-pointer hover:text-blue-800 hover:underline"
                                onclick="window.location.href='curtain/56.html'">56</span>
                        </p>
                    </div>

                    <!-- 更多设置 -->
                    <div class="text-center">
                        <h4 class="text-lg font-medium text-gray-700 mb-2">更多设置</h4>
                        <iframe src="curtain/57.html" class="responsive-iframe mx-auto mb-2"></iframe>
                        <p class="text-sm text-gray-600"><span
                                class="text-blue-600 cursor-pointer hover:text-blue-800 hover:underline"
                                onclick="window.location.href='curtain/57.html'">57</span>
                        </p>
                    </div>

                    <!-- 占位 -->
                    <div class="text-center">
                        <h4 class="text-lg font-medium text-gray-700 mb-2 opacity-0">占位</h4>
                        <div class="w-400 h-850 mx-auto"></div>
                    </div>
                </div>

                <!-- 行程校准流程页面 -->
                <div
                    class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2 md:gap-6 lg:gap-8 iframe-container mb-6">
                    <!-- 校准准备 -->
                    <div class="text-center">
                        <h4 class="text-lg font-medium text-gray-700 mb-2">校准准备</h4>
                        <iframe src="curtain/58.html" class="responsive-iframe mx-auto mb-2"></iframe>
                        <p class="text-sm text-gray-600"><span
                                class="text-blue-600 cursor-pointer hover:text-blue-800 hover:underline"
                                onclick="window.location.href='curtain/58.html'">58</span>
                        </p>
                    </div>

                    <!-- 校准进行中 -->
                    <div class="text-center">
                        <h4 class="text-lg font-medium text-gray-700 mb-2">校准进行中</h4>
                        <iframe src="curtain/59.html" class="responsive-iframe mx-auto mb-2"></iframe>
                        <p class="text-sm text-gray-600"><span
                                class="text-blue-600 cursor-pointer hover:text-blue-800 hover:underline"
                                onclick="window.location.href='curtain/59.html'">59</span>
                        </p>
                    </div>

                    <!-- 用户确认 -->
                    <div class="text-center">
                        <h4 class="text-lg font-medium text-gray-700 mb-2">用户确认</h4>
                        <iframe src="curtain/60.html" class="responsive-iframe mx-auto mb-2"></iframe>
                        <p class="text-sm text-gray-600"><span
                                class="text-blue-600 cursor-pointer hover:text-blue-800 hover:underline"
                                onclick="window.location.href='curtain/60.html'">60</span>
                        </p>
                    </div>
                </div>

                <!-- 行程校准流程页面 - 第二行 -->
                <div
                    class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2 md:gap-6 lg:gap-8 iframe-container mb-6">
                    <!-- 校准完成 -->
                    <div class="text-center">
                        <h4 class="text-lg font-medium text-gray-700 mb-2">校准完成</h4>
                        <iframe src="curtain/62.html" class="responsive-iframe mx-auto mb-2"></iframe>
                        <p class="text-sm text-gray-600"><span
                                class="text-blue-600 cursor-pointer hover:text-blue-800 hover:underline"
                                onclick="window.location.href='curtain/62.html'">62</span>
                        </p>
                    </div>

                    <!-- 占位 -->
                    <div class="text-center">
                        <h4 class="text-lg font-medium text-gray-700 mb-2 opacity-0">占位</h4>
                        <div class="w-400 h-850 mx-auto"></div>
                    </div>

                    <!-- 占位 -->
                    <div class="text-center">
                        <h4 class="text-lg font-medium text-gray-700 mb-2 opacity-0">占位</h4>
                        <div class="w-400 h-850 mx-auto"></div>
                    </div>
                </div>
            </div>

            <!-- 开合帘详情 -->
            <div class="mb-10">
                <h3 class="text-xl font-semibold text-blue-600 mb-6">2.5 开合帘</h3>

                <!-- 开合帘功能页面 -->
                <div
                    class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2 md:gap-6 lg:gap-8 iframe-container mb-6">
                    <!-- 设备详情 -->
                    <div class="text-center">
                        <h4 class="text-lg font-medium text-gray-700 mb-2">设备详情</h4>
                        <iframe src="curtain/63.html" class="responsive-iframe mx-auto mb-2"></iframe>
                        <p class="text-sm text-gray-600"><span
                                class="text-blue-600 cursor-pointer hover:text-blue-800 hover:underline"
                                onclick="window.location.href='curtain/63.html'">63</span>
                        </p>
                    </div>

                    <!-- 更多设置 -->
                    <div class="text-center">
                        <h4 class="text-lg font-medium text-gray-700 mb-2">更多设置</h4>
                        <iframe src="curtain/64.html" class="responsive-iframe mx-auto mb-2"></iframe>
                        <p class="text-sm text-gray-600"><span
                                class="text-blue-600 cursor-pointer hover:text-blue-800 hover:underline"
                                onclick="window.location.href='curtain/64.html'">64</span>
                        </p>
                    </div>

                    <!-- 占位 -->
                    <div class="text-center">
                        <h4 class="text-lg font-medium text-gray-700 mb-2 opacity-0">占位</h4>
                        <div class="w-400 h-850 mx-auto"></div>
                    </div>
                </div>
            </div>

        </div>
    </div>
</body>

</html>