<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>发现设备</title>
    <link href="https://unpkg.com/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            background: linear-gradient(180deg, #e6f3ff 0%, #f0f8ff 50%, #e8f4f8 100%);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        .device-card {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        .device-icon {
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(5px);
        }
        .status-icon {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .status-added {
            background: #10b981;
            color: white;
        }
        .status-adding {
            background: #3b82f6;
            color: white;
        }
        .status-error {
            background: #ef4444;
            color: white;
        }
        .status-available {
            background: #3b82f6;
            color: white;
        }

        /* 搜索状态样式 */
        .search-status {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(59, 130, 246, 0.2);
            box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);
        }

        /* 时间圆环样式 */
        .time-circle {
            position: relative;
            width: 60px;
            height: 60px;
            border: 3px solid #e5e7eb;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            background: white;
        }

        .time-circle::before {
            content: '';
            position: absolute;
            top: -3px;
            left: -3px;
            width: 60px;
            height: 60px;
            border: 3px solid transparent;
            border-top-color: #3b82f6;
            border-radius: 50%;
            transform: rotate(270deg);
        }
    </style>
</head>
<body class="h-screen">
    <!-- iOS状态栏 -->
    <div class="fixed top-0 left-0 right-0 text-black text-sm px-4 py-2 flex justify-between items-center z-50"
        style="background: linear-gradient(180deg, #e6f3ff 0%, #f0f8ff 100%);">
        <div class="font-semibold">
            9:41
        </div>
        <div class="flex items-center space-x-1">
            <i class="fas fa-signal text-xs"></i>
            <i class="fas fa-wifi text-xs"></i>
            <i class="fas fa-battery-full text-xs"></i>
        </div>
    </div>

    <!-- 顶部导航 -->
    <div class="fixed top-10 left-0 right-0 bg-transparent px-4 py-3 z-40">
        <div class="flex items-center justify-center relative">
            <button class="absolute left-0">
                <i class="fas fa-chevron-left text-gray-600 text-lg"></i>
            </button>
            <h1 class="text-lg font-semibold text-gray-800">添加设备</h1>
        </div>
    </div>

    <!-- 主内容区域 -->
    <div class="pt-24 pb-20 px-4">
        <!-- 搜索状态指示器 -->
        <div class="search-status rounded-xl p-4 mb-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                        <i class="fas fa-search text-blue-600"></i>
                    </div>
                    <div>
                        <div class="font-semibold text-gray-800">正在搜索设备</div>
                        <div class="text-sm text-gray-500">搜索中...</div>
                    </div>
                </div>
                <div class="time-circle">
                    <div class="text-center">
                        <div class="text-lg font-bold text-blue-600">45</div>
                        <div class="text-xs text-gray-500">秒</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 搜索提示 -->
        <div class="mb-6 px-2">
            <p class="text-gray-600 text-sm text-center">
                设备会逐个出现，请耐心等待
            </p>
        </div>

        <!-- 标题 -->
        <div class="mb-4">
            <h2 class="text-lg font-semibold text-gray-800">发现的设备</h2>
        </div>

        <!-- 发现的设备列表 -->
        <div class="space-y-3">
            <!-- 门窗传感器 - 第一个发现 -->
            <div class="device-card rounded-xl p-4">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="device-icon w-12 h-12 rounded-lg flex items-center justify-center mr-3">
                            <i class="fas fa-door-open text-gray-600 text-lg"></i>
                        </div>
                        <div>
                            <h3 class="font-semibold text-gray-800">门窗传感器</h3>
                            <p class="text-xs text-gray-500">DN码: 1xZSc3vKYPP9JVnG6q45</p>

                        </div>
                    </div>
                    <div class="status-icon status-available">
                        <i class="fas fa-plus text-sm"></i>
                    </div>
                </div>
            </div>

            <!-- 人体传感器 - 第二个发现 -->
            <div class="device-card rounded-xl p-4">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="device-icon w-12 h-12 rounded-lg flex items-center justify-center mr-3">
                            <i class="fas fa-user text-gray-600 text-lg"></i>
                        </div>
                        <div>
                            <h3 class="font-semibold text-gray-800">人体传感器</h3>
                            <p class="text-xs text-gray-500">DN码: 1xZSc3vKYPP9JVnG6q14</p>

                        </div>
                    </div>
                    <div class="status-icon status-adding">
                        <i class="fas fa-spinner text-sm"></i>
                    </div>
                </div>
            </div>

            <!-- 燃气传感器 - 第三个发现 -->
            <div class="device-card rounded-xl p-4">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="device-icon w-12 h-12 rounded-lg flex items-center justify-center mr-3">
                            <i class="fas fa-fire text-gray-600 text-lg"></i>
                        </div>
                        <div>
                            <h3 class="font-semibold text-gray-800">燃气传感器</h3>
                            <p class="text-xs text-gray-500">DN码: 1xZSc3vKYPP9JVnG6qS4</p>

                        </div>
                    </div>
                    <div class="status-icon status-added">
                        <i class="fas fa-check text-sm"></i>
                    </div>
                </div>
            </div>
        </div>



        <!-- 按型号添加入口 -->
        <div class="text-center py-6">
            <p class="text-gray-500 text-sm mb-2 text-center">设备无法自动发现？</p>
            <div class="flex justify-center">
                <a href="#" class="text-blue-600 font-medium text-sm inline-flex items-center">
                    <i class="fas fa-plus text-blue-600 text-xs mr-1"></i>
                    按型号添加
                </a>
            </div>
        </div>
    </div>

    <!-- 底部按钮 -->
    <div class="fixed bottom-8 left-0 right-0 bg-transparent px-4">
        <button class="w-full bg-blue-500 text-white py-4 rounded-xl font-medium text-base">
            一键绑定
        </button>
    </div>

    <!-- iOS底部指示器 -->
    <div class="fixed bottom-0 left-0 right-0 flex justify-center pt-4 pb-2 bg-transparent">
        <div class="w-32 h-1 bg-black rounded-full"></div>
    </div>
</body>
</html>
