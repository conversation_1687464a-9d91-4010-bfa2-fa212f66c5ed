<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>固件升级</title>
    <link href="https://unpkg.com/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            background: linear-gradient(180deg, #e6f3ff 0%, #f0f8ff 50%, #e8f4f8 100%);
            overflow-x: hidden;
        }
        .device-card {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body class="h-screen">
    <!-- iOS状态栏 -->
    <div class="fixed top-0 left-0 right-0 text-black text-sm px-4 py-2 flex justify-between items-center z-50" style="background: linear-gradient(180deg, #e6f3ff 0%, #f0f8ff 100%);">
        <div class="font-semibold">
            9:41
        </div>
        <div class="flex items-center space-x-1">
            <i class="fas fa-signal text-xs"></i>
            <i class="fas fa-wifi text-xs"></i>
            <i class="fas fa-battery-full text-xs"></i>
        </div>
    </div>

    <!-- 顶部导航 -->
    <div class="fixed top-10 left-0 right-0 bg-transparent px-4 py-3 z-40">
        <div class="flex items-center justify-center relative">
            <button class="absolute left-0">
                <i class="fas fa-chevron-left text-gray-600 text-lg"></i>
            </button>
            <h1 class="text-lg font-semibold text-gray-800">固件升级</h1>
        </div>
    </div>

    <!-- 主内容区域 -->
    <div class="pt-24 pb-8">
        <!-- 当前版本信息 -->
        <div class="device-card mx-4 mt-4 rounded-xl p-4 mb-6">
            <div class="flex items-center justify-between mb-4">
                <h2 class="text-lg font-semibold text-gray-800">当前版本</h2>
                <div class="flex items-center">
                    <div class="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                    <span class="text-sm text-green-600 font-medium">最新</span>
                </div>
            </div>
            <div class="space-y-3">
                <div class="flex justify-between">
                    <span class="text-gray-600">固件版本</span>
                    <span class="font-medium text-gray-800">V2.1.3</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-600">发布时间</span>
                    <span class="font-medium text-gray-800">2024-01-15</span>
                </div>

            </div>
        </div>

        <!-- 注意事项 -->
        <div class="mx-4 mb-6 px-2">
            <div class="flex items-start">
                <div class="flex-shrink-0 mr-3">
                    <i class="fas fa-exclamation-triangle text-amber-500 text-lg"></i>
                </div>
                <div class="flex-1">
                    <p class="text-amber-600 font-medium text-sm mb-3">升级前请注意</p>
                    <div class="space-y-2 text-sm text-gray-600 leading-relaxed">
                        <p>• 升级过程中请勿断电或重启设备</p>
                        <p>• 升级时间约需5-10分钟，请耐心等待</p>
                        <p>• 建议在网络稳定时进行升级</p>
                        <p>• 升级完成后设备将自动重启</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 底部按钮 -->
    <div class="fixed bottom-8 left-0 right-0 bg-transparent px-4">
        <button class="w-full bg-gray-300 text-gray-500 py-4 rounded-xl font-medium text-base cursor-not-allowed" disabled>
            已是最新版本
        </button>
    </div>

    <!-- iOS底部指示器 -->
    <div class="fixed bottom-0 left-0 right-0 flex justify-center pt-4 pb-2 bg-transparent">
        <div class="w-32 h-1 bg-black rounded-full"></div>
    </div>
</body>
</html>
