<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>蓝牙Mesh组网</title>
    <link href="https://unpkg.com/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            background: linear-gradient(180deg, #e6f3ff 0%, #f0f8ff 100%);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        .device-card {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .toggle-switch {
            position: relative;
            width: 50px;
            height: 28px;
            background: #4ade80;
            border-radius: 14px;
            cursor: pointer;
        }
        .toggle-switch::before {
            content: '';
            position: absolute;
            top: 2px;
            right: 2px;
            width: 24px;
            height: 24px;
            background: white;
            border-radius: 50%;
        }
        .speaker-device {
            width: 120px;
            height: 160px;
            background: #2d2d2d;
            border-radius: 12px;
            position: relative;
            margin: 0 auto 20px;
        }
        .speaker-device::before {
            content: '';
            position: absolute;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            width: 8px;
            height: 8px;
            background: #666;
            border-radius: 50%;
        }
        .speaker-device::after {
            content: '';
            position: absolute;
            bottom: 30px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 60px;
            background: repeating-linear-gradient(
                45deg,
                #444,
                #444 2px,
                #333 2px,
                #333 4px
            );
            border-radius: 4px;
        }
        .empty-state {
            text-align: center;
            padding: 40px 20px;
        }
        .empty-icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 16px;
            opacity: 0.3;
        }
    </style>
</head>
<body class="h-screen">
    <!-- iOS状态栏 -->
    <div class="fixed top-0 left-0 right-0 text-black text-sm px-4 py-2 flex justify-between items-center z-50" style="background: linear-gradient(180deg, #e6f3ff 0%, #f0f8ff 100%);">
        <div class="font-semibold">
            9:41
        </div>
        <div class="flex items-center space-x-1">
            <i class="fas fa-signal text-xs"></i>
            <i class="fas fa-wifi text-xs"></i>
            <i class="fas fa-battery-full text-xs"></i>
        </div>
    </div>

    <!-- 顶部导航 -->
    <div class="fixed top-10 left-0 right-0 bg-transparent px-4 py-3 z-40">
        <div class="flex items-center justify-center relative">
            <button class="absolute left-0">
                <i class="fas fa-chevron-left text-gray-600 text-lg"></i>
            </button>
            <h1 class="text-lg font-semibold text-gray-800">5G CPE</h1>
        </div>
    </div>

    <!-- 主内容区域 -->
    <div class="flex-1 overflow-y-auto pt-24 pb-20 px-4">
        <!-- 设备展示 -->
        <div class="text-center mb-8">
            <div class="speaker-device"></div>
        </div>

        <!-- 蓝牙Mesh网关开关 -->
        <div class="device-card rounded-2xl p-4 mb-6">
            <div class="flex items-center justify-between">
                <div class="flex-1">
                    <h3 class="text-lg font-semibold text-gray-800 mb-2">蓝牙Mesh组网</h3>
                    <p class="text-sm text-gray-600 leading-relaxed">
                        开启后，Wi-Fi可能会受影响，建议将其置于信号好的位置。蓝牙播放音乐时可能会影响部分蓝牙网关功能，如无法查询状态等。
                    </p>
                </div>
                <div class="ml-4">
                    <div class="toggle-switch"></div>
                </div>
            </div>
        </div>

        <!-- 空状态 -->
        <div class="empty-state">
            <div class="empty-icon">
                <svg viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <!-- 星球图标 -->
                    <circle cx="50" cy="45" r="20" fill="#d1d5db" stroke="#9ca3af" stroke-width="2"/>
                    <ellipse cx="50" cy="45" rx="30" ry="8" fill="none" stroke="#9ca3af" stroke-width="1.5"/>
                    <ellipse cx="50" cy="45" rx="8" ry="30" fill="none" stroke="#9ca3af" stroke-width="1.5"/>
                    <!-- 底部线条 -->
                    <line x1="20" y1="80" x2="80" y2="80" stroke="#d1d5db" stroke-width="2"/>
                    <line x1="25" y1="85" x2="75" y2="85" stroke="#d1d5db" stroke-width="1.5"/>
                    <line x1="30" y1="90" x2="70" y2="90" stroke="#d1d5db" stroke-width="1"/>
                </svg>
            </div>
            <p class="text-gray-500 text-sm mb-6">没有子设备连接该网关</p>

        </div>
    </div>

    <!-- 底部按钮 -->
    <div class="fixed bottom-8 left-0 right-0 px-4 bg-transparent">
        <button class="w-full bg-blue-500 text-white py-4 rounded-xl font-medium text-base flex items-center justify-center">
            添加蓝牙Mesh设备
        </button>
    </div>

    <!-- iOS底部指示器 -->
    <div class="fixed bottom-0 left-0 right-0 flex justify-center pt-4 pb-2 bg-transparent">
        <div class="w-32 h-1 bg-black rounded-full"></div>
    </div>
</body>
</html>
