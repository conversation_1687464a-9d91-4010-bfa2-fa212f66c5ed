<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>一键测速 - 合觅</title>
    <link href="https://unpkg.com/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            background: linear-gradient(180deg, #e6f3ff 0%, #f0f8ff 50%, #e8f4f8 100%);
        }
        .device-card {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        .test-button {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            width: 200px;
            height: 200px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            font-weight: bold;
            color: white;
            box-shadow: 0 0 30px rgba(79, 172, 254, 0.3);
        }
        .speed-ring {
            width: 280px;
            height: 280px;
            border: 2px solid rgba(59, 130, 246, 0.2);
            border-radius: 50%;
            position: relative;
        }
        .speed-ring::before {
            content: '';
            position: absolute;
            top: -10px;
            left: -10px;
            right: -10px;
            bottom: -10px;
            border: 1px solid rgba(59, 130, 246, 0.1);
            border-radius: 50%;
        }
        .tab-item {
            background: rgba(255, 255, 255, 0.8);
            border-radius: 12px;
            padding: 16px;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .tab-item i {
            font-size: 24px;
            margin-bottom: 8px;
            color: #3b82f6;
        }
    </style>
</head>
<body class="h-screen">
    <!-- iOS状态栏 -->
    <div class="fixed top-0 left-0 right-0 text-black text-sm px-4 py-2 flex justify-between items-center z-50"
        style="background: linear-gradient(180deg, #e6f3ff 0%, #f0f8ff 100%);">
        <div class="font-semibold">9:41</div>
        <div class="flex items-center space-x-1">
            <i class="fas fa-signal text-xs"></i>
            <i class="fas fa-wifi text-xs"></i>
            <i class="fas fa-battery-full text-xs"></i>
        </div>
    </div>

    <!-- 顶部导航栏 -->
    <div class="fixed top-10 left-0 right-0 bg-transparent px-4 py-3 z-40">
        <div class="flex items-center justify-center relative">
            <button class="absolute left-0">
                <i class="fas fa-chevron-left text-gray-600 text-lg"></i>
            </button>
            <h1 class="text-lg font-semibold text-gray-800">网络测速</h1>
        </div>
    </div>

    <!-- 主内容区域 -->
    <div class="pt-24 pb-20 px-4 h-full flex flex-col">

        <!-- 测速区域 -->
        <div class="flex-1 relative">
            <!-- 测速按钮 - 固定位置 -->
            <div class="absolute top-1/3 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                <div class="speed-ring flex items-center justify-center mb-8">
                    <button class="test-button">
                        <div class="text-center">
                            <div class="text-2xl font-bold mb-1">开始</div>
                            <div class="text-2xl font-bold">测速</div>
                        </div>
                    </button>
                </div>
                <!-- 说明文字 -->
                <div class="text-center">
                    <p class="text-gray-600 text-sm">检测已绑定5G CPE的网络速度，不是手机WiFi的网速</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 设备信息区域 -->
    <div class="fixed bottom-8 left-0 right-0 px-4 bg-transparent">
        <div class="device-card rounded-2xl p-4">
            <!-- 设备信息 -->
            <div class="grid grid-cols-3 gap-4">
                <div class="info-item">
                    <i class="fas fa-network-wired"></i>
                    <p class="text-xs text-gray-500 mb-1">家庭网关</p>
                    <p class="text-sm text-gray-800 font-medium">5G CPE</p>
                </div>
                <div class="info-item">
                    <i class="fas fa-clock"></i>
                    <p class="text-xs text-gray-500 mb-1">预计耗时</p>
                    <p class="text-sm text-gray-800 font-medium">20s</p>
                </div>
                <div class="info-item">
                    <i class="fas fa-map-marker-alt"></i>
                    <p class="text-xs text-gray-500 mb-1">设备所在地</p>
                    <p class="text-sm text-gray-800 font-medium">广州市</p>
                </div>
            </div>
        </div>
    </div>

    <!-- iOS底部指示器 -->
    <div class="fixed bottom-0 left-0 right-0 flex justify-center pt-4 pb-2 bg-transparent">
        <div class="w-32 h-1 bg-black rounded-full"></div>
    </div>
</body>
</html>
