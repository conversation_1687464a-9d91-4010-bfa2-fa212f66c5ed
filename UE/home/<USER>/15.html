<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测速结果 - 合觅</title>
    <link href="https://unpkg.com/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            background: linear-gradient(180deg, #e6f3ff 0%, #f0f8ff 50%, #e8f4f8 100%);
        }
        .device-card {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        .speed-number {
            font-size: 64px;
            font-weight: bold;
            color: #4facfe;
        }
        .speed-unit {
            font-size: 24px;
            color: #4facfe;
            margin-left: 8px;
        }
        .metric-item {
            text-align: center;
            padding: 20px;
        }
        .metric-value {
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 8px;
        }
        .metric-label {
            font-size: 14px;
            color: #6b7280;
        }

        .info-item {
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            text-align: center;
        }
        .info-item i {
            font-size: 20px;
            margin-bottom: 8px;
            color: #3b82f6;
        }
        .restart-button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 12px 32px;
            border-radius: 24px;
            font-size: 16px;
            font-weight: 500;
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
        }
    </style>
</head>
<body class="h-screen overflow-y-auto">
    <!-- iOS状态栏 -->
    <div class="fixed top-0 left-0 right-0 text-black text-sm px-4 py-2 flex justify-between items-center z-50"
        style="background: linear-gradient(180deg, #e6f3ff 0%, #f0f8ff 100%);">
        <div class="font-semibold">9:41</div>
        <div class="flex items-center space-x-1">
            <i class="fas fa-signal text-xs"></i>
            <i class="fas fa-wifi text-xs"></i>
            <i class="fas fa-battery-full text-xs"></i>
        </div>
    </div>

    <!-- 顶部导航栏 -->
    <div class="fixed top-10 left-0 right-0 bg-transparent px-4 py-3 z-40">
        <div class="flex items-center justify-center relative">
            <button class="absolute left-0">
                <i class="fas fa-chevron-left text-gray-600 text-lg"></i>
            </button>
            <h1 class="text-lg font-semibold text-gray-800">网络测速</h1>
        </div>
    </div>

    <!-- 主内容区域 -->
    <div class="pt-24 pb-20 px-4 h-full flex flex-col">

        <!-- 测速结果区域 -->
        <div class="flex-1 flex flex-col items-center">
            <!-- 上半部分：测速结果 -->
            <div class="flex-1 flex flex-col items-center justify-center">
                <!-- 主要测速结果 -->
                <div class="text-center mb-8">
                    <div class="flex items-baseline justify-center mb-4">
                        <span class="speed-number">27.3</span>
                        <span class="speed-unit">Mbps</span>
                    </div>
                    <p class="text-sm text-gray-600">测速完成</p>
                </div>

                <!-- 上传下载速度 -->
                <div class="grid grid-cols-2 gap-8 mb-8">
                    <div class="metric-item">
                        <div class="flex items-center justify-center mb-2">
                            <i class="fas fa-arrow-up text-blue-500 mr-2"></i>
                            <span class="text-gray-700 text-sm">上传</span>
                        </div>
                        <div class="metric-value text-blue-500">16.5 <span class="text-base">Mbps</span></div>
                    </div>
                    <div class="metric-item">
                        <div class="flex items-center justify-center mb-2">
                            <i class="fas fa-arrow-down text-green-500 mr-2"></i>
                            <span class="text-gray-700 text-sm">下载</span>
                        </div>
                        <div class="metric-value text-green-500">27.3 <span class="text-base">Mbps</span></div>
                    </div>
                </div>
            </div>

            <!-- 下半部分：重新测速按钮 -->
            <div class="pb-32">
                <button class="restart-button">
                    重新测速
                </button>
            </div>
        </div>
    </div>

    <!-- 设备信息区域 -->
    <div class="fixed bottom-8 left-0 right-0 px-4 bg-transparent">
        <div class="device-card rounded-2xl p-4">
            <!-- 设备信息 -->
            <div class="grid grid-cols-3 gap-4">
                <div class="info-item">
                    <i class="fas fa-network-wired"></i>
                    <p class="text-xs text-gray-500 mb-1">家庭网关</p>
                    <p class="text-sm text-gray-800 font-medium">5G CPE</p>
                </div>
                <div class="info-item">
                    <i class="fas fa-clock"></i>
                    <p class="text-xs text-gray-500 mb-1">测速耗时</p>
                    <p class="text-sm text-gray-800 font-medium">18s</p>
                </div>
                <div class="info-item">
                    <i class="fas fa-map-marker-alt"></i>
                    <p class="text-xs text-gray-500 mb-1">设备所在地</p>
                    <p class="text-sm text-gray-800 font-medium">广州市</p>
                </div>
            </div>
        </div>
    </div>

    <!-- iOS底部指示器 -->
    <div class="fixed bottom-0 left-0 right-0 flex justify-center pt-4 pb-2 bg-transparent">
        <div class="w-32 h-1 bg-black rounded-full"></div>
    </div>
</body>
</html>
