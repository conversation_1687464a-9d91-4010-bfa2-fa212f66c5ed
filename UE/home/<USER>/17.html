<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>子设备管理</title>
    <link href="https://unpkg.com/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            background: linear-gradient(180deg, #e6f3ff 0%, #f0f8ff 50%, #e8f4f8 100%);
        }

        .device-card {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>

<body class="min-h-screen">
    <!-- iOS状态栏 -->
    <div class="fixed top-0 left-0 right-0 text-black text-sm px-4 py-2 flex justify-between items-center z-50"
        style="background: linear-gradient(180deg, #e6f3ff 0%, #f0f8ff 100%);">
        <div class="font-semibold">
            9:41
        </div>
        <div class="flex items-center space-x-1">
            <i class="fas fa-signal text-xs"></i>
            <i class="fas fa-wifi text-xs"></i>
            <i class="fas fa-battery-full text-xs"></i>
        </div>
    </div>

    <!-- 顶部导航 -->
    <div class="fixed top-10 left-0 right-0 bg-transparent px-4 py-3 z-40">
        <div class="flex items-center justify-center relative">
            <button class="absolute left-0">
                <i class="fas fa-chevron-left text-gray-600 text-lg"></i>
            </button>
            <h1 class="text-lg font-semibold text-gray-800">子设备管理</h1>
            <button class="absolute right-0 text-blue-500 text-sm font-medium">
                <i class="fas fa-plus mr-1"></i>添加设备
            </button>
        </div>
    </div>

    <!-- 主内容区域 -->
    <div class="pt-24 pb-20">
        <!-- 网关信息 -->
        <div class="device-card mx-4 mt-4 rounded-2xl p-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-blue-500 rounded-xl flex items-center justify-center mr-3">
                        <i class="fa-solid fa-charging-station text-white text-lg"></i>
                    </div>
                    <div>
                        <h2 class="text-lg font-bold text-gray-800">5G CPE</h2>
                        <p class="text-sm text-gray-500">网关设备 · 在线</p>
                    </div>
                </div>
                <div class="text-right">
                    <p class="text-sm font-medium text-gray-800">已连接设备</p>
                    <p class="text-lg font-bold text-blue-500">6台</p>
                </div>
            </div>
        </div>

        <!-- 设备筛选 -->
        <div class="mx-4 mt-6">
            <div class="flex justify-between items-center mb-4">
                <div class="flex space-x-3">
                    <button class="bg-blue-500 text-white px-4 py-2 rounded-lg text-sm font-medium">
                        全部 (6)
                    </button>
                    <button class="bg-gray-100 text-gray-600 px-4 py-2 rounded-lg text-sm font-medium">
                        在线 (5)
                    </button>
                    <button class="bg-gray-100 text-gray-600 px-4 py-2 rounded-lg text-sm font-medium">
                        离线 (1)
                    </button>
                </div>
                <button class="bg-red-500 text-white px-3 py-2 rounded-lg text-sm font-medium">
                    <i class="fas fa-trash mr-1"></i>移除
                </button>
            </div>
        </div>

        <!-- 子设备网格 -->
        <div class="mx-4 grid grid-cols-2 gap-3">
            <!-- 智能音箱 -->
            <div class="device-card rounded-xl p-4 text-center">
                <div class="w-12 h-12 bg-purple-500 rounded-lg flex items-center justify-center mx-auto mb-3">
                    <i class="fas fa-music text-white text-lg"></i>
                </div>
                <h3 class="font-semibold text-gray-800 mb-1">智能音箱</h3>
                <p class="text-xs text-gray-500">离线</p>
            </div>

            <!-- 智能屏 -->
            <div class="device-card rounded-xl p-4 text-center">
                <div class="w-12 h-12 bg-blue-500 rounded-lg flex items-center justify-center mx-auto mb-3">
                    <i class="fas fa-tablet text-white text-lg"></i>
                </div>
                <h3 class="font-semibold text-gray-800 mb-1">智能屏</h3>
                <p class="text-xs text-green-600">在线</p>
            </div>

            <!-- 智能开关 -->
            <div class="device-card rounded-xl p-4 text-center">
                <div class="w-12 h-12 bg-green-500 rounded-lg flex items-center justify-center mx-auto mb-3">
                    <i class="fas fa-power-off text-white text-lg"></i>
                </div>
                <h3 class="font-semibold text-gray-800 mb-1">智能开关</h3>
                <p class="text-xs text-green-600">在线</p>
            </div>

            <!-- 人在传感器 -->
            <div class="device-card rounded-xl p-4 text-center">
                <div class="w-12 h-12 bg-green-500 rounded-lg flex items-center justify-center mx-auto mb-3">
                    <i class="fas fa-user text-white text-lg"></i>
                </div>
                <h3 class="font-semibold text-gray-800 mb-1">人在传感器</h3>
                <p class="text-xs text-green-600">在线</p>
            </div>

            <!-- 智能锁 -->
            <div class="device-card rounded-xl p-4 text-center">
                <div class="w-12 h-12 bg-red-500 rounded-lg flex items-center justify-center mx-auto mb-3">
                    <i class="fas fa-lock text-white text-lg"></i>
                </div>
                <h3 class="font-semibold text-gray-800 mb-1">智能锁</h3>
                <p class="text-xs text-green-600">在线</p>
            </div>

            <!-- 智能旋钮 -->
            <div class="device-card rounded-xl p-4 text-center">
                <div class="w-12 h-12 rounded-lg flex items-center justify-center mx-auto mb-3" style="background: linear-gradient(135deg, #f97316 0%, #ea580c 100%);">
                    <i class="fas fa-adjust text-white text-lg"></i>
                </div>
                <h3 class="font-semibold text-gray-800 mb-1">智能旋钮</h3>
                <p class="text-xs text-green-600">在线</p>
            </div>
        </div>


    </div>

    <!-- iOS底部指示器 -->
    <div class="fixed bottom-0 left-0 right-0 flex justify-center pt-4 pb-2 bg-transparent">
        <div class="w-32 h-1 bg-black rounded-full"></div>
    </div>
</body>

</html>
