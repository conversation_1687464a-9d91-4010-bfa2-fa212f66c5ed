<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>流量清单</title>
    <link href="https://unpkg.com/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            background: linear-gradient(180deg, #e6f3ff 0%, #f0f8ff 50%, #e8f4f8 100%);
        }
        .device-card {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body class="h-screen">
    <!-- iOS状态栏 -->
    <div class="fixed top-0 left-0 right-0 text-black text-sm px-4 py-2 flex justify-between items-center z-50" style="background: linear-gradient(180deg, #e6f3ff 0%, #f0f8ff 100%);">
        <div class="font-semibold">
            9:41
        </div>
        <div class="flex items-center space-x-1">
            <i class="fas fa-signal text-xs"></i>
            <i class="fas fa-wifi text-xs"></i>
            <i class="fas fa-battery-full text-xs"></i>
        </div>
    </div>

    <!-- 顶部导航 -->
    <div class="fixed top-10 left-0 right-0 bg-transparent px-4 py-3 z-40">
        <div class="flex items-center justify-center relative">
            <button class="absolute left-0">
                <i class="fas fa-chevron-left text-gray-600 text-lg"></i>
            </button>
            <h1 class="text-lg font-semibold text-gray-800">流量清单</h1>
        </div>
    </div>

    <!-- 主内容区域 -->
    <div class="flex-1 overflow-y-auto pt-24 pb-8">
        <!-- 流量概览 -->
        <div class="bg-gradient-to-br from-blue-500 to-purple-600 mx-4 mt-4 rounded-2xl p-6 text-white">
            <div class="text-center mb-4">
                <h2 class="text-2xl font-bold mb-1">15.2GB</h2>
                <p class="text-sm opacity-90">本月已用流量</p>
            </div>

            <!-- 进度条 -->
            <div class="mb-4">
                <div class="w-full bg-white bg-opacity-20 rounded-full h-3">
                    <div class="bg-white h-3 rounded-full" style="width: 15.2%"></div>
                </div>
                <div class="flex justify-between text-sm mt-2 opacity-90">
                    <span>0GB</span>
                    <span>100GB</span>
                </div>
            </div>

            <div class="grid grid-cols-3 gap-4 text-center">
                <div>
                    <p class="text-lg font-bold">84.8GB</p>
                    <p class="text-xs opacity-80">剩余流量</p>
                </div>
                <div>
                    <p class="text-lg font-bold">1.2GB</p>
                    <p class="text-xs opacity-80">今日已用</p>
                </div>
                <div>
                    <p class="text-lg font-bold">15天</p>
                    <p class="text-xs opacity-80">距离月结</p>
                </div>
            </div>
        </div>

        <!-- 今日流量详情 -->
        <div class="mx-4 mt-4">
            <div class="device-card rounded-2xl p-4 mb-4">
                <h4 class="font-semibold text-gray-800 mb-3">今日流量详情</h4>
                <div class="flex items-center justify-between mb-4">
                    <span class="text-gray-600">总计：1.2GB</span>
                    <span class="text-sm text-gray-500">更新时间：刚刚</span>
                </div>

                <!-- 24小时流量图表 -->
                <div class="mb-4">
                    <div class="flex items-end justify-between h-24 bg-gray-50 rounded-lg p-3">
                        <!-- 0-2点 -->
                        <div class="flex flex-col items-center">
                            <div class="bg-blue-400 rounded-t" style="height: 8px; width: 8px; margin-bottom: 2px;"></div>
                            <span class="text-xs text-gray-500">0</span>
                        </div>
                        <!-- 2-4点 -->
                        <div class="flex flex-col items-center">
                            <div class="bg-blue-400 rounded-t" style="height: 6px; width: 8px; margin-bottom: 2px;"></div>
                            <span class="text-xs text-gray-500">2</span>
                        </div>
                        <!-- 4-6点 -->
                        <div class="flex flex-col items-center">
                            <div class="bg-blue-400 rounded-t" style="height: 4px; width: 8px; margin-bottom: 2px;"></div>
                            <span class="text-xs text-gray-500">4</span>
                        </div>
                        <!-- 6-8点 -->
                        <div class="flex flex-col items-center">
                            <div class="bg-green-500 rounded-t" style="height: 20px; width: 8px; margin-bottom: 2px;"></div>
                            <span class="text-xs text-gray-500">6</span>
                        </div>
                        <!-- 8-10点 -->
                        <div class="flex flex-col items-center">
                            <div class="bg-green-500 rounded-t" style="height: 35px; width: 8px; margin-bottom: 2px;"></div>
                            <span class="text-xs text-gray-500">8</span>
                        </div>
                        <!-- 10-12点 -->
                        <div class="flex flex-col items-center">
                            <div class="bg-green-500 rounded-t" style="height: 28px; width: 8px; margin-bottom: 2px;"></div>
                            <span class="text-xs text-gray-500">10</span>
                        </div>
                        <!-- 12-14点 -->
                        <div class="flex flex-col items-center">
                            <div class="bg-orange-500 rounded-t" style="height: 45px; width: 8px; margin-bottom: 2px;"></div>
                            <span class="text-xs text-gray-500">12</span>
                        </div>
                        <!-- 14-16点 -->
                        <div class="flex flex-col items-center">
                            <div class="bg-orange-500 rounded-t" style="height: 38px; width: 8px; margin-bottom: 2px;"></div>
                            <span class="text-xs text-gray-500">14</span>
                        </div>
                        <!-- 16-18点 -->
                        <div class="flex flex-col items-center">
                            <div class="bg-orange-500 rounded-t" style="height: 32px; width: 8px; margin-bottom: 2px;"></div>
                            <span class="text-xs text-gray-500">16</span>
                        </div>
                        <!-- 18-20点 -->
                        <div class="flex flex-col items-center">
                            <div class="bg-purple-500 rounded-t" style="height: 55px; width: 8px; margin-bottom: 2px;"></div>
                            <span class="text-xs text-gray-500">18</span>
                        </div>
                        <!-- 20-22点 -->
                        <div class="flex flex-col items-center">
                            <div class="bg-purple-500 rounded-t" style="height: 48px; width: 8px; margin-bottom: 2px;"></div>
                            <span class="text-xs text-gray-500">20</span>
                        </div>
                        <!-- 22-24点 -->
                        <div class="flex flex-col items-center">
                            <div class="bg-purple-500 rounded-t" style="height: 25px; width: 8px; margin-bottom: 2px;"></div>
                            <span class="text-xs text-gray-500">22</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 设备流量排行 -->
            <div class="device-card rounded-2xl p-4 mb-4">
                <h4 class="font-semibold text-gray-800 mb-3">设备流量排行</h4>
                <div class="space-y-3">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-gray-800 rounded-lg flex items-center justify-center mr-3">
                                <i class="fas fa-mobile-alt text-white text-sm"></i>
                            </div>
                            <span class="text-gray-800 font-medium">iPhone 13 Pro</span>
                        </div>
                        <span class="text-blue-600 font-semibold">0.5GB</span>
                    </div>

                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-gray-600 rounded-lg flex items-center justify-center mr-3">
                                <i class="fas fa-laptop text-white text-sm"></i>
                            </div>
                            <span class="text-gray-800 font-medium">MacBook Pro</span>
                        </div>
                        <span class="text-blue-600 font-semibold">0.4GB</span>
                    </div>

                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-purple-500 rounded-lg flex items-center justify-center mr-3">
                                <i class="fas fa-tv text-white text-sm"></i>
                            </div>
                            <span class="text-gray-800 font-medium">小米电视</span>
                        </div>
                        <span class="text-blue-600 font-semibold">0.2GB</span>
                    </div>

                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center mr-3">
                                <i class="fas fa-tablet-alt text-white text-sm"></i>
                            </div>
                            <span class="text-gray-800 font-medium">iPad Air</span>
                        </div>
                        <span class="text-blue-600 font-semibold">0.1GB</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- iOS底部指示器 -->
    <div class="fixed bottom-0 left-0 right-0 flex justify-center pt-4 pb-2 bg-transparent">
        <div class="w-32 h-1 bg-black rounded-full"></div>
    </div>
</body>
</html>
