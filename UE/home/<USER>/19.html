<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Wi-Fi管理</title>
    <link href="https://unpkg.com/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            background: linear-gradient(180deg, #e6f3ff 0%, #f0f8ff 50%, #e8f4f8 100%);
        }
        .device-card {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body class="h-screen">
    <!-- iOS状态栏 -->
    <div class="fixed top-0 left-0 right-0 text-black text-sm px-4 py-2 flex justify-between items-center z-50" style="background: linear-gradient(180deg, #e6f3ff 0%, #f0f8ff 100%);">
        <div class="font-semibold">
            9:41
        </div>
        <div class="flex items-center space-x-1">
            <i class="fas fa-signal text-xs"></i>
            <i class="fas fa-wifi text-xs"></i>
            <i class="fas fa-battery-full text-xs"></i>
        </div>
    </div>

    <!-- 顶部导航 -->
    <div class="fixed top-10 left-0 right-0 bg-transparent px-4 py-3 z-40">
        <div class="flex items-center justify-center relative">
            <button class="absolute left-0">
                <i class="fas fa-chevron-left text-gray-600 text-lg"></i>
            </button>
            <h1 class="text-lg font-semibold text-gray-800">Wi-Fi管理</h1>
        </div>
    </div>

    <!-- 主内容区域 -->
    <div class="flex-1 overflow-y-auto pt-24 pb-8">
        <!-- Wi-Fi状态 -->
        <div class="device-card mx-4 mt-4 rounded-2xl p-4">
            <div class="flex items-center justify-between mb-4">
                <div class="flex bg-gray-100 rounded-xl p-1">
                    <div class="w-12 h-12 bg-green-500 rounded-xl flex items-center justify-center mr-3">
                        <i class="fas fa-wifi text-white text-xl"></i>
                    </div>
                    <div>
                        <h2 class="font-semibold text-gray-800">Wi-Fi网络</h2>
                        <p class="text-sm text-green-600">已开启 · 8台设备连接</p>
                    </div>
                </div>
                <div class="w-14 h-8 bg-green-500 rounded-full relative">
                    <div class="w-6 h-6 bg-white rounded-full absolute right-1 top-1"></div>
                </div>
            </div>

            <div class="bg-gray-50 rounded-xl p-3">
                <div class="flex justify-between items-center">
                    <span class="text-sm text-gray-600">当前状态</span>
                    <span class="text-sm font-semibold text-green-600">2.4G和5G网络均已开启</span>
                </div>
            </div>
        </div>

        <!-- Wi-Fi设置 -->
        <div class="mx-4 mt-4">
            <h3 class="text-lg font-semibold text-gray-800 mb-3">网络设置</h3>

            <!-- 2.4G网络 -->
            <div class="device-card rounded-2xl p-4 mb-4">
                <div class="flex items-center justify-between mb-3">
                    <h4 class="font-semibold text-gray-800 flex items-center">
                        <i class="fas fa-wifi text-blue-500 mr-2"></i>
                        2.4G网络
                    </h4>
                    <span class="text-sm text-green-600 bg-green-50 px-2 py-1 rounded-full">已开启</span>
                </div>
                <div class="space-y-3">
                    <div class="flex items-center justify-between">
                        <span class="text-gray-600">网络名称</span>
                        <div class="flex items-center">
                            <span class="text-gray-800 font-medium mr-2">Homie_WiFi</span>
                            <button class="text-blue-500 text-sm">修改</button>
                        </div>
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="text-gray-600">网络密码</span>
                        <div class="flex items-center">
                            <span class="text-gray-800 font-medium mr-2">••••••••</span>
                            <button class="text-blue-500 text-sm">修改</button>
                        </div>
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="text-gray-600">连接设备</span>
                        <span class="text-blue-500 font-medium">5台设备</span>
                    </div>
                </div>
            </div>

            <!-- 5G网络 -->
            <div class="device-card rounded-2xl p-4 mb-4">
                <div class="flex items-center justify-between mb-3">
                    <h4 class="font-semibold text-gray-800 flex items-center">
                        <i class="fas fa-wifi text-purple-500 mr-2"></i>
                        5G网络
                    </h4>
                    <span class="text-sm text-green-600 bg-green-50 px-2 py-1 rounded-full">已开启</span>
                </div>
                <div class="space-y-3">
                    <div class="flex items-center justify-between">
                        <span class="text-gray-600">网络名称</span>
                        <div class="flex items-center">
                            <span class="text-gray-800 font-medium mr-2">Homie_WiFi_5G</span>
                            <button class="text-blue-500 text-sm">修改</button>
                        </div>
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="text-gray-600">网络密码</span>
                        <div class="flex items-center">
                            <span class="text-gray-800 font-medium mr-2">••••••••</span>
                            <button class="text-blue-500 text-sm">修改</button>
                        </div>
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="text-gray-600">连接设备</span>
                        <span class="text-blue-500 font-medium">3台设备</span>
                    </div>
                </div>
            </div>

            <!-- 高级设置 -->
            <div class="device-card rounded-2xl p-4 mb-4">
                <h4 class="font-semibold text-gray-800 mb-3 flex items-center">
                    <i class="fas fa-cog text-orange-500 mr-2"></i>
                    高级设置
                </h4>
                <div class="space-y-4">
                    <div class="flex items-center justify-between">
                        <div>
                            <span class="text-gray-800 font-medium">Wi-Fi隐藏</span>
                            <p class="text-sm text-gray-500">隐藏所有Wi-Fi热点</p>
                        </div>
                        <div class="w-12 h-6 bg-gray-300 rounded-full relative">
                            <div class="w-5 h-5 bg-white rounded-full absolute left-0.5 top-0.5"></div>
                        </div>
                    </div>

                    <div class="border-t border-gray-100 pt-4">
                        <div class="flex items-center justify-between mb-2">
                            <span class="text-gray-800 font-medium">流量上限设置</span>
                            <button class="text-blue-500 text-sm">设置</button>
                        </div>
                        <p class="text-sm text-gray-500">每日Wi-Fi流量消耗上限</p>
                        <div class="mt-2 flex flex-wrap gap-2">
                            <span class="bg-blue-50 text-blue-600 px-3 py-1 rounded-full text-sm">3G</span>
                            <span class="bg-gray-100 text-gray-600 px-3 py-1 rounded-full text-sm">5G</span>
                            <span class="bg-blue-500 text-white px-3 py-1 rounded-full text-sm">10G</span>
                            <span class="bg-gray-100 text-gray-600 px-3 py-1 rounded-full text-sm">20G</span>
                            <span class="bg-gray-100 text-gray-600 px-3 py-1 rounded-full text-sm">50G</span>
                            <span class="bg-gray-100 text-gray-600 px-3 py-1 rounded-full text-sm">不限量</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 快捷操作 -->
            <div class="device-card rounded-2xl p-4">
                <h4 class="font-semibold text-gray-800 mb-3">快捷操作</h4>
                <div class="grid grid-cols-2 gap-3">
                    <button class="bg-blue-50 text-blue-600 py-3 px-4 rounded-xl text-sm font-medium">
                        <i class="fas fa-qrcode mb-1 block"></i>
                        分享密码
                    </button>
                    <button class="bg-green-50 text-green-600 py-3 px-4 rounded-xl text-sm font-medium">
                        <i class="fas fa-users mb-1 block"></i>
                        连接管理
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- iOS底部指示器 -->
    <div class="fixed bottom-0 left-0 right-0 flex justify-center pt-4 pb-2 bg-transparent">
        <div class="w-32 h-1 bg-black rounded-full"></div>
    </div>
</body>
</html>
