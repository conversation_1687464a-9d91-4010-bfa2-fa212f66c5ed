<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>5G CPE设备管理</title>
    <link href="https://unpkg.com/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            background: linear-gradient(180deg, #e6f3ff 0%, #f0f8ff 50%, #e8f4f8 100%);
        }
        .device-card {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body class="h-screen">
    <!-- iOS状态栏 -->
    <div class="fixed top-0 left-0 right-0 text-black text-sm px-4 py-2 flex justify-between items-center z-50" style="background: linear-gradient(180deg, #e6f3ff 0%, #f0f8ff 100%);">
        <div class="font-semibold">
            9:41
        </div>
        <div class="flex items-center space-x-1">
            <i class="fas fa-signal text-xs"></i>
            <i class="fas fa-wifi text-xs"></i>
            <i class="fas fa-battery-full text-xs"></i>
        </div>
    </div>

    <!-- 顶部导航 -->
    <div class="fixed top-10 left-0 right-0 bg-transparent px-4 py-3 z-40">
        <div class="flex items-center justify-center relative">
            <button class="absolute left-0">
                <i class="fas fa-chevron-left text-gray-600 text-lg"></i>
            </button>
            <h1 class="text-lg font-semibold text-gray-800">5G CPE</h1>
            <button class="absolute right-0">
                <i class="fas fa-ellipsis-h text-gray-600 text-lg"></i>
            </button>
        </div>
    </div>

    <!-- 主内容区域 -->
    <div class="flex-1 overflow-y-auto pt-24 pb-8">
        <!-- 活动横幅 -->
        <div class="mx-4 mt-3 mb-4 bg-gradient-to-r from-orange-50 to-red-50 rounded-xl p-3 border border-orange-100">
            <div class="flex items-center">
                <div class="flex-shrink-0 mr-3">
                    <i class="fas fa-volume-up text-red-500 text-lg"></i>
                </div>
                <div class="flex-1">
                    <span class="text-gray-700 text-sm">您有待领取的爱奇艺白金会员，</span>
                    <button class="text-orange-500 font-medium text-sm ml-1 underline">点击立即领取</button>
                </div>
            </div>
        </div>

        <!-- 设备状态与实时监控区 -->
        <div class="device-card mx-4 mt-3 rounded-2xl p-3 mb-4">
            <!-- 设备标题 -->
            <div class="flex items-center justify-between mb-3">
                <div class="flex items-center">
                    <div class="w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center mr-3">
                        <i class="fa-solid fa-charging-station text-white"></i>
                    </div>
                    <div>
                        <div class="flex items-center mb-1">
                            <h2 class="text-lg font-bold text-gray-800 mr-2">5G CPE</h2>
                        
                        </div>
                        <div class="flex items-center text-sm">
                            <i class="fas fa-check-circle text-green-500 mr-1"></i>
                            <span class="text-gray-600 mr-1">已实名</span>
                            <span class="text-gray-800 font-medium">138****8888</span>
                        </div>
                    </div>
                </div>
                <div class="flex items-center">
                    <i class="fas fa-wifi text-green-500 mr-1"></i>
                    <span class="text-xs text-green-600 font-medium">在线</span>
                </div>
            </div>

            <!-- 设备信息 -->
            <div class="grid grid-cols-2 gap-3 mb-3">
                <div class="bg-blue-50 rounded-lg p-2.5 flex items-center justify-center">
                    <i class="fas fa-barcode text-blue-500 mr-2"></i>
                    <span class="text-sm font-bold text-gray-800">AiX720000460</span>
                </div>
                <div class="bg-green-50 rounded-lg p-2.5 flex items-center justify-center">
                    <i class="fas fa-shield-alt text-green-500 mr-2"></i>
                    <span class="text-sm font-bold text-green-600">已认证</span>
                </div>
            </div>

            <!-- 实时网速 -->
            <div class="grid grid-cols-2 gap-4 mb-4">
                <div class="text-center">
                    <div class="flex items-center justify-center mb-2">
                        <i class="fas fa-arrow-up text-blue-500 mr-1"></i>
                        <span class="text-lg font-bold text-blue-600">15.2</span>
                        <span class="text-xs text-gray-500 ml-1">Mbps</span>
                    </div>
                    <p class="text-xs text-gray-500">上传速度</p>
                </div>
                <div class="text-center">
                    <div class="flex items-center justify-center mb-2">
                        <i class="fas fa-arrow-down text-green-500 mr-1"></i>
                        <span class="text-lg font-bold text-green-600">85.6</span>
                        <span class="text-xs text-gray-500 ml-1">Mbps</span>
                    </div>
                    <p class="text-xs text-gray-500">下载速度</p>
                </div>
            </div>

            <!-- 网络信息 -->
            <div class="grid grid-cols-3 gap-3 text-center">
                <div class="flex flex-col items-center p-3">
                    <div class="flex items-center justify-center mb-1">
                        <i class="fas fa-signal text-blue-500 mr-1"></i>
                        <span class="text-sm font-bold text-gray-800">强</span>
                    </div>
                    <p class="text-xs text-gray-500">5G信号</p>
                </div>
                <div class="flex flex-col items-center p-3">
                    <div class="flex items-center justify-center mb-1">
                        <i class="fas fa-devices text-purple-500 mr-1"></i>
                        <span class="text-sm font-bold text-gray-800">8</span>
                    </div>
                    <p class="text-xs text-gray-500">连接设备</p>
                </div>
                <div class="flex flex-col items-center p-3">
                    <div class="flex items-center justify-center mb-1">
                        <i class="fas fa-broadcast-tower text-orange-500 mr-1"></i>
                        <span class="text-sm font-bold text-gray-800">5G</span>
                    </div>
                    <p class="text-xs text-gray-500">网络模式</p>
                </div>
            </div>
        </div>

        <!-- 流量套餐信息区 -->
        <div class="device-card mx-4 rounded-2xl p-4 mb-4">
            <!-- 套餐与流量信息 -->
            <div class="grid grid-cols-4 gap-2 text-center mb-4">
                <div class="flex flex-col items-center">
                    <p class="text-lg font-bold text-gray-800 mb-1 whitespace-nowrap">包月</p>
                    <p class="text-xs text-gray-500 whitespace-nowrap">当月套餐</p>
                </div>
                <div class="flex flex-col items-center">
                    <p class="text-lg font-bold text-red-500 mb-1 whitespace-nowrap">6.55<span class="text-sm">GB</span></p>
                    <p class="text-xs text-gray-500 whitespace-nowrap">已用流量</p>
                </div>
                <div class="flex flex-col items-center">
                    <p class="text-lg font-bold text-gray-800 mb-1 whitespace-nowrap">--</p>
                    <p class="text-xs text-gray-500 whitespace-nowrap">下月套餐</p>
                </div>
                <div class="flex flex-col items-center">
                    <p class="text-lg font-bold text-gray-800 mb-1 whitespace-nowrap">06<span class="text-sm">月</span>06<span class="text-sm">日</span></p>
                    <p class="text-xs text-gray-500 whitespace-nowrap">月结日</p>
                </div>
            </div>

            <!-- 用户状态 -->
            <div class="flex items-center justify-between">
                <span class="bg-yellow-100 text-orange-600 text-xs px-2 py-1 rounded flex items-center whitespace-nowrap">
                    <span class="mr-1">👑</span>新用户
                </span>
                <span class="text-xs text-orange-500 whitespace-nowrap">套餐到期日 2025.6.6</span>
            </div>
        </div>

        <!-- 快捷操作 -->
        <div class="mx-4 mt-4">
            <div class="grid grid-cols-2 gap-3">
                <button class="device-card rounded-xl p-3 flex items-center justify-center">
                    <i class="fas fa-tachometer-alt text-blue-500 mr-2"></i>
                    <span class="text-sm font-medium text-gray-700">网络测速</span>
                </button>
                <button class="device-card rounded-xl p-3 flex items-center justify-center">
                    <i class="fas fa-chart-bar text-blue-500 mr-2"></i>
                    <span class="text-sm font-medium text-gray-700">流量清单</span>
                </button>
            </div>
        </div>

        <!-- 设备功能 -->
        <div class="mx-4 mt-4">
            <div class="device-card rounded-2xl p-4">
                <h3 class="text-lg font-semibold text-gray-800 mb-3">设备管理</h3>
                <div class="grid grid-cols-3 gap-4">
                    <button class="flex flex-col items-center py-3">
                        <div class="w-12 h-12 bg-purple-500 rounded-2xl flex items-center justify-center mb-2">
                            <i class="fas fa-network-wired text-white text-lg"></i>
                        </div>
                        <span class="text-xs text-gray-700">路由管理</span>
                    </button>
                    <button class="flex flex-col items-center py-3">
                        <div class="w-12 h-12 bg-blue-500 rounded-2xl flex items-center justify-center mb-2">
                            <i class="fas fa-wifi text-white text-lg"></i>
                        </div>
                        <span class="text-xs text-gray-700">Wi-Fi管理</span>
                    </button>
                    <button class="flex flex-col items-center py-3">
                        <div class="w-12 h-12 bg-indigo-500 rounded-2xl flex items-center justify-center mb-2">
                            <i class="fas fa-broadcast-tower text-white text-lg"></i>
                        </div>
                        <span class="text-xs text-gray-700">Mesh组网</span>
                    </button>

                </div>
            </div>
        </div>

        <!-- 智能家居 -->
        <div class="mx-4 mt-4">
            <div class="device-card rounded-2xl p-4">
                <h3 class="text-lg font-semibold text-gray-800 mb-3">智能家居</h3>
                <div class="grid grid-cols-3 gap-4">
                    <button class="flex flex-col items-center py-3">
                        <div class="w-12 h-12 bg-purple-500 rounded-2xl flex items-center justify-center mb-2">
                            <i class="fas fa-comments text-white text-lg"></i>
                        </div>
                        <span class="text-xs text-gray-700">对话记录</span>
                    </button>
                    <button class="flex flex-col items-center py-3">
                        <div class="w-12 h-12 bg-blue-500 rounded-2xl flex items-center justify-center mb-2">
                            <i class="fas fa-thermometer-half text-white text-lg"></i>
                        </div>
                        <span class="text-xs text-gray-700">子设备管理</span>
                    </button>
                    <button class="flex flex-col items-center py-3">
                        <div class="w-12 h-12 bg-indigo-500 rounded-2xl flex items-center justify-center mb-2">
                            <i class="fas fa-home text-white text-lg"></i>
                        </div>
                        <span class="text-xs text-gray-700">场景设置</span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- iOS底部指示器 -->
    <div class="fixed bottom-0 left-0 right-0 flex justify-center pt-4 pb-2 bg-transparent">
        <div class="w-32 h-1 bg-black rounded-full"></div>
    </div>
</body>
</html>
