<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>添加子设备</title>
    <link href="https://unpkg.com/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            background: linear-gradient(180deg, #e6f3ff 0%, #f0f8ff 50%, #e8f4f8 100%);
            overflow-x: hidden;
        }
        .device-card {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        .category-item {
            background: rgba(255, 255, 255, 0.8);
            border-left: 3px solid transparent;
        }
        .category-item.active {
            background: rgba(255, 255, 255, 0.95);
            border-left: 3px solid #06b6d4;
            color: #06b6d4;
        }
        .category-item.new-product {
            border-left: 3px solid #f97316;
            color: #f97316;
        }
        .device-item {
            background: rgba(255, 255, 255, 0.9);
            border: 1px solid #e5e7eb;
        }

    </style>
</head>
<body class="h-screen">
    <!-- iOS状态栏 -->
    <div class="fixed top-0 left-0 right-0 text-black text-sm px-4 py-2 flex justify-between items-center z-50" style="background: linear-gradient(180deg, #e6f3ff 0%, #f0f8ff 100%);">
        <div class="font-semibold">
            9:41
        </div>
        <div class="flex items-center space-x-1">
            <i class="fas fa-signal text-xs"></i>
            <i class="fas fa-wifi text-xs"></i>
            <i class="fas fa-battery-full text-xs"></i>
        </div>
    </div>

    <!-- 顶部导航 -->
    <div class="fixed top-10 left-0 right-0 bg-transparent px-4 py-3 z-40">
        <div class="flex items-center justify-center relative">
            <button class="absolute left-0">
                <i class="fas fa-chevron-left text-gray-600 text-lg"></i>
            </button>
            <h1 class="text-lg font-semibold text-gray-800">选择型号</h1>
        </div>
    </div>

    <!-- 搜索栏 -->
    <div class="fixed top-20 left-0 right-0 px-4 py-3 z-30" style="background: linear-gradient(180deg, #e6f3ff 0%, #f0f8ff 100%);">
        <div class="relative">
            <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
            <input type="text" placeholder="搜索" class="w-full pl-10 pr-4 py-2 bg-gray-100 rounded-lg text-sm">
        </div>
    </div>

    <!-- 主内容区域 -->
    <div class="flex h-full pt-32 pb-8">
        <!-- 左侧设备分类 -->
        <div class="w-24 bg-gray-50 border-r border-gray-200 overflow-y-auto">
            <div class="py-2">
                <!-- 新品推荐 -->
                <div class="category-item new-product px-3 py-4 text-center cursor-pointer">
                    <i class="fas fa-fire text-lg mb-1"></i>
                    <p class="text-xs font-medium">新品推荐</p>
                </div>

                <!-- 融合网关 -->
                <div class="category-item px-3 py-4 text-center cursor-pointer">
                    <i class="fa-solid fa-charging-station text-lg mb-1"></i>
                    <p class="text-xs font-medium">融合网关</p>
                </div>

                <!-- 智能音箱 -->
                <div class="category-item px-3 py-4 text-center cursor-pointer">
                    <i class="fas fa-music text-lg mb-1"></i>
                    <p class="text-xs font-medium">智能音箱</p>
                </div>

                <!-- 语音助手 -->
                <div class="category-item px-3 py-4 text-center cursor-pointer">
                    <i class="fas fa-microphone text-lg mb-1"></i>
                    <p class="text-xs font-medium">语音助手</p>
                </div>

                <!-- 智能开关 -->
                <div class="category-item active px-3 py-4 text-center cursor-pointer">
                    <i class="fas fa-power-off text-lg mb-1"></i>
                    <p class="text-xs font-medium">智能开关</p>
                </div>

                <!-- 智能照明 -->
                <div class="category-item px-3 py-4 text-center cursor-pointer">
                    <i class="fas fa-lightbulb text-lg mb-1"></i>
                    <p class="text-xs font-medium">智能照明</p>
                </div>

                <!-- 驱动器 -->
                <div class="category-item px-3 py-4 text-center cursor-pointer">
                    <i class="fas fa-microchip text-lg mb-1"></i>
                    <p class="text-xs font-medium">驱动器</p>
                </div>

                <!-- 窗帘电机 -->
                <div class="category-item px-3 py-4 text-center cursor-pointer">
                    <i class="fas fa-window-maximize text-lg mb-1"></i>
                    <p class="text-xs font-medium">窗帘电机</p>
                </div>

                <!-- 安防设备 -->
                <div class="category-item px-3 py-4 text-center cursor-pointer">
                    <i class="fas fa-shield-alt text-lg mb-1"></i>
                    <p class="text-xs font-medium">安防设备</p>
                </div>

                <!-- 智能锁 -->
                <div class="category-item px-3 py-4 text-center cursor-pointer">
                    <i class="fas fa-lock text-lg mb-1"></i>
                    <p class="text-xs font-medium">智能锁</p>
                </div>

                <!-- 晾衣机 -->
                <div class="category-item px-3 py-4 text-center cursor-pointer">
                    <i class="fas fa-tshirt text-lg mb-1"></i>
                    <p class="text-xs font-medium">晾衣机</p>
                </div>
            </div>
        </div>

        <!-- 右侧设备列表 -->
        <div class="flex-1 overflow-y-auto">
            <!-- 分类标题 -->
            <div class="px-4 py-4 bg-gray-50 border-b border-gray-200">
                <h2 id="category-title" class="text-base font-semibold text-gray-700">智能开关</h2>
            </div>

            <!-- 设备网格 -->
            <div class="p-4">
                <!-- 智能开关设备 -->
                <div id="category-智能开关" class="category-content grid grid-cols-3 gap-3">
                    <!-- 智能旋钮 -->
                    <div class="device-item rounded-lg p-3 text-center cursor-pointer flex flex-col items-center">
                        <div class="w-12 h-12 bg-gray-100 rounded-lg mx-auto mb-2 flex items-center justify-center">
                            <i class="fas fa-circle-notch text-gray-400 text-lg"></i>
                        </div>
                        <p class="text-xs font-medium text-gray-800">智能旋钮</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- iOS底部指示器 -->
    <div class="fixed bottom-0 left-0 right-0 flex justify-center pt-4 pb-2 bg-transparent">
        <div class="w-32 h-1 bg-black rounded-full"></div>
    </div>
</body>
</html>
