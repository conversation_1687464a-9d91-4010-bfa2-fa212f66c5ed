<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>子设备添加-无网关</title>
    <link href="https://unpkg.com/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            background: linear-gradient(180deg, #e6f3ff 0%, #f0f8ff 100%);
            overflow: hidden;
        }
    </style>
</head>
<body class="h-screen">
    <!-- iOS状态栏 -->
    <div class="fixed top-0 left-0 right-0 text-black text-sm px-4 py-2 flex justify-between items-center z-50" style="background: linear-gradient(180deg, #e6f3ff 0%, #f0f8ff 100%);">
        <div class="font-semibold">
            9:41
        </div>
        <div class="flex items-center space-x-1">
            <i class="fas fa-signal text-xs"></i>
            <i class="fas fa-wifi text-xs"></i>
            <i class="fas fa-battery-full text-xs"></i>
        </div>
    </div>

    <!-- 主内容区域 -->
    <div class="pt-12 h-full flex flex-col">
        <!-- 顶部导航栏 -->
        <div class="fixed top-10 left-0 right-0 bg-transparent px-4 py-3 z-40">
            <div class="flex items-center justify-center relative">
                <button class="absolute left-0">
                    <i class="fas fa-chevron-left text-gray-600 text-lg"></i>
                </button>
                <h1 class="text-lg font-semibold text-gray-800">添加设备</h1>
            </div>
        </div>

        <!-- 内容区域 -->
        <div class="flex-1 px-4 py-6 pt-12 pb-32 flex flex-col items-center justify-center">
            <!-- 主要提示信息 -->
            <div class="text-center mb-8 max-w-xs">
                <h2 class="text-lg font-semibold text-gray-800 mb-6">未绑定网关</h2>

                <!-- 空状态插画 -->
                <div class="mb-8 flex justify-center">
                    <svg width="120" height="120" viewBox="0 0 120 120" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <!-- 背景圆 -->
                        <circle cx="60" cy="60" r="60" fill="#F8F9FA"/>

                        <!-- 设备轮廓 -->
                        <rect x="35" y="45" width="50" height="30" rx="4" fill="none" stroke="#D1D5DB" stroke-width="2" stroke-dasharray="4,4"/>

                        <!-- 设备屏幕 -->
                        <rect x="40" y="50" width="40" height="20" rx="2" fill="#E5E7EB"/>

                        <!-- 信号线 -->
                        <path d="M50 35 Q60 25 70 35" stroke="#D1D5DB" stroke-width="2" fill="none" stroke-linecap="round"/>
                        <path d="M45 40 Q60 28 75 40" stroke="#D1D5DB" stroke-width="1.5" fill="none" stroke-linecap="round"/>

                        <!-- 问号 -->
                        <circle cx="60" cy="85" r="8" fill="#9CA3AF"/>
                        <text x="60" y="90" text-anchor="middle" fill="white" font-size="10" font-weight="bold">?</text>
                    </svg>
                </div>
                <div class="space-y-3">
                    <p class="text-gray-600 text-sm leading-relaxed">
                        添加子设备需要先绑定网关设备<br>
                        （5G CPE、智能音箱）
                    </p>
                </div>
            </div>

        </div>
    </div>

    <!-- 底部按钮 -->
    <div class="fixed bottom-8 left-0 right-0 px-4 bg-transparent">
        <div class="space-y-3">
            <button class="w-full bg-blue-500 text-white py-4 rounded-xl font-medium text-base">
                添加网关
            </button>
            <button class="w-full bg-white text-gray-700 py-4 rounded-xl font-medium text-base border border-gray-200">
                稍后再说
            </button>
        </div>
    </div>

    <!-- iOS底部指示器 -->
    <div class="fixed bottom-0 left-0 right-0 flex justify-center pt-4 pb-2 bg-transparent">
        <div class="w-32 h-1 bg-black rounded-full"></div>
    </div>

</body>
</html>
