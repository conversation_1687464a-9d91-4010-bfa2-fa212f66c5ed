<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>完成绑定</title>
    <link href="https://unpkg.com/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            background: linear-gradient(180deg, #e6f3ff 0%, #f0f8ff 50%, #e8f4f8 100%);
            overflow-x: hidden;
        }
        
        .progress-circle {
            width: 200px;
            height: 200px;
            border-radius: 50%;
            background: conic-gradient(from 0deg, #3b82f6 0deg, #3b82f6 180deg, #e5e7eb 180deg, #e5e7eb 360deg);
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
        }

        .progress-inner {
            width: 160px;
            height: 160px;
            border-radius: 50%;
            background: white;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
        }

        .progress-text {
            font-size: 3rem;
            font-weight: bold;
            color: #3b82f6;
            line-height: 1;
        }
        
        .step-completed {
            color: #3b82f6;
        }

        .step-current {
            color: #6b7280;
        }
    </style>
</head>
<body class="h-screen">
    <!-- iOS状态栏 -->
    <div class="fixed top-0 left-0 right-0 text-black text-sm px-4 py-2 flex justify-between items-center z-50" style="background: linear-gradient(180deg, #e6f3ff 0%, #f0f8ff 100%);">
        <div class="font-semibold">
            9:41
        </div>
        <div class="flex items-center space-x-1">
            <i class="fas fa-signal text-xs"></i>
            <i class="fas fa-wifi text-xs"></i>
            <i class="fas fa-battery-full text-xs"></i>
        </div>
    </div>

    <!-- 顶部导航 -->
    <div class="fixed top-10 left-0 right-0 bg-transparent px-4 py-3 z-40">
        <div class="flex items-center justify-center relative">
            <button class="absolute left-0">
                <i class="fas fa-chevron-left text-gray-600 text-lg"></i>
            </button>
            <h1 class="text-lg font-semibold text-gray-800">添加设备</h1>
        </div>
    </div>

    <!-- 主内容区域 -->
    <div class="flex-1 px-4 pt-20 pb-32 overflow-y-auto">
        <div class="flex flex-col items-center justify-center min-h-full py-8">
            <!-- 进度圆环 -->
            <div class="progress-circle mb-8">
                <div class="progress-inner">
                    <div class="progress-text">50%</div>
                </div>
            </div>

            <!-- 状态文字 -->
            <div class="text-center mb-12">
                <h2 class="text-xl font-bold text-gray-800 mb-2">正在绑定</h2>
                <p class="text-gray-600 text-sm leading-relaxed">
                    完成配网需要1分钟左右时间，请耐心等候
                </p>
            </div>

            <!-- 步骤列表 -->
            <div class="w-full max-w-sm space-y-4 mb-12">
                <!-- 步骤1 - 已完成 -->
                <div class="step-item step-completed flex items-center">
                    <div class="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center mr-4">
                        <i class="fas fa-check text-white text-xs"></i>
                    </div>
                    <span class="text-base font-medium">找到设备</span>
                </div>

                <!-- 步骤2 - 进行中 -->
                <div class="step-item step-current flex items-center">
                    <div class="w-6 h-6 border-2 border-gray-400 rounded-full flex items-center justify-center mr-4">
                        <div class="w-2 h-2 bg-gray-400 rounded-full"></div>
                    </div>
                    <span class="text-base font-medium">完成设备绑定</span>
                </div>
            </div>
        </div>
    </div>

    <!-- 底部按钮 -->
    <div class="fixed bottom-8 left-0 right-0 bg-transparent px-4">
        <button class="w-full bg-gray-300 text-gray-500 py-4 rounded-xl font-medium text-base cursor-not-allowed" disabled>
            立即体验
        </button>
    </div>

    <!-- iOS底部指示器 -->
    <div class="fixed bottom-0 left-0 right-0 flex justify-center pt-4 pb-2 bg-transparent">
        <div class="w-32 h-1 bg-black rounded-full"></div>
    </div>
</body>
</html>
