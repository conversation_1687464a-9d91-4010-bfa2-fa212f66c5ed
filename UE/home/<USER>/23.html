<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>绑定失败</title>
    <link href="https://unpkg.com/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            background: linear-gradient(180deg, #e6f3ff 0%, #f0f8ff 50%, #e8f4f8 100%);
            overflow-x: hidden;
        }
        
        .error-circle {
            width: 200px;
            height: 200px;
            border-radius: 50%;
            background: #ef4444;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
        }
        
        .error-inner {
            width: 160px;
            height: 160px;
            border-radius: 50%;
            background: white;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
        }
        
        .error-icon {
            font-size: 4rem;
            color: #ef4444;
            line-height: 1;
        }
        
        .tips-item {
            display: flex;
            align-items: flex-start;
            margin-bottom: 16px;
        }
        
        .tips-dot {
            width: 6px;
            height: 6px;
            background: #6b7280;
            border-radius: 50%;
            margin-top: 8px;
            margin-right: 12px;
            flex-shrink: 0;
        }
    </style>
</head>
<body class="h-screen">
    <!-- iOS状态栏 -->
    <div class="fixed top-0 left-0 right-0 text-black text-sm px-4 py-2 flex justify-between items-center z-50" style="background: linear-gradient(180deg, #e6f3ff 0%, #f0f8ff 100%);">
        <div class="font-semibold">
            9:41
        </div>
        <div class="flex items-center space-x-1">
            <i class="fas fa-signal text-xs"></i>
            <i class="fas fa-wifi text-xs"></i>
            <i class="fas fa-battery-full text-xs"></i>
        </div>
    </div>

    <!-- 顶部导航 -->
    <div class="fixed top-10 left-0 right-0 bg-transparent px-4 py-3 z-40">
        <div class="flex items-center justify-center relative">
            <button class="absolute left-0">
                <i class="fas fa-chevron-left text-gray-600 text-lg"></i>
            </button>
            <h1 class="text-lg font-semibold text-gray-800">添加设备</h1>
        </div>
    </div>

    <!-- 主内容区域 -->
    <div class="flex-1 px-4 pt-20 pb-32 overflow-y-auto">
        <div class="flex flex-col items-center justify-center min-h-full py-8">
            <!-- 错误圆环 -->
            <div class="error-circle mb-8">
                <div class="error-inner">
                    <div class="error-icon">
                        <i class="fas fa-times"></i>
                    </div>
                </div>
            </div>

            <!-- 状态文字 -->
            <div class="text-center mb-8">
                <h2 class="text-xl font-bold text-gray-800 mb-2">绑定失败！</h2>
                <p class="text-gray-600 text-sm leading-relaxed">
                    可以尝试以下操作然后再试一次
                </p>
            </div>

            <!-- 操作提示 -->
            <div class="w-full max-w-sm mb-12 px-2">
                <div class="flex items-start">
                    <div class="flex-shrink-0 mr-3">
                        <i class="fas fa-lightbulb text-amber-500 text-lg"></i>
                    </div>
                    <div class="flex-1">
                        <p class="text-amber-600 font-medium text-sm mb-2">解决方案</p>
                        <p class="text-gray-600 text-sm leading-relaxed">
                            请确保设备已经插电并进入配网模式，然后重新尝试绑定
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 底部按钮区域 -->
    <div class="fixed bottom-8 left-0 right-0 bg-transparent px-4">
        <!-- 主要按钮 -->
        <button class="w-full bg-blue-500 text-white py-4 rounded-xl font-medium text-base mb-4">
            再试一次
        </button>

        <!-- 次要按钮 -->
        <button class="w-full bg-white text-gray-700 py-4 rounded-xl font-medium text-base border border-gray-200">
            稍后再说
        </button>
    </div>

    <!-- iOS底部指示器 -->
    <div class="fixed bottom-0 left-0 right-0 flex justify-center pt-4 pb-2 bg-transparent">
        <div class="w-32 h-1 bg-black rounded-full"></div>
    </div>
</body>
</html>
