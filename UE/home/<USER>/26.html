<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>按键动作配置</title>
    <link href="https://unpkg.com/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            background: linear-gradient(180deg, #e6f3ff 0%, #f0f8ff 50%, #e8f4f8 100%);
        }
        .action-card {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        .action-item {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border-radius: 12px;
            padding: 8px;
            margin: 3px 0;
            border: 2px solid transparent;
        }
        .action-item.selected {
            border-color: #3b82f6;
            background: rgba(59, 130, 246, 0.1);
        }
        .action-item.disabled {
            opacity: 0.6;
            background: rgba(255, 255, 255, 0.5);
        }
    </style>
</head>
<body class="h-screen">
    <!-- iOS状态栏 -->
    <div class="fixed top-0 left-0 right-0 text-black text-sm px-4 py-2 flex justify-between items-center z-50"
        style="background: linear-gradient(180deg, #e6f3ff 0%, #f0f8ff 100%);">
        <div class="font-semibold">9:41</div>
        <div class="flex items-center space-x-1">
            <i class="fas fa-signal text-xs"></i>
            <i class="fas fa-wifi text-xs"></i>
            <i class="fas fa-battery-full text-xs"></i>
        </div>
    </div>

    <!-- 顶部导航 -->
    <div class="fixed top-10 left-0 right-0 bg-transparent px-4 py-3 z-40">
        <div class="flex items-center justify-between">
            <div class="flex items-center">
                <i class="fas fa-chevron-left text-gray-600 text-lg"></i>
            </div>
            <h1 class="text-lg font-semibold text-gray-800">高级设置</h1>
            <div class="w-6"></div>
        </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="pt-24 pb-8 px-4">
        <!-- 当前配置显示 -->
        <div class="action-card rounded-2xl p-4 mt-4">
            <div class="flex items-center mb-4">
                <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                    <i class="fas fa-chevron-up text-blue-600"></i>
                </div>
                <div>
                    <div class="font-semibold text-gray-800">上键</div>
                    <div class="text-sm text-gray-500">已选设备：客厅灯</div>
                </div>
            </div>
        </div>

        <!-- 动作配置区域 -->
        <div class="action-card rounded-2xl p-4 mt-8">
            <h3 class="text-md font-semibold text-gray-800 mb-4">选择动作类型</h3>
            
            <div class="space-y-2">
                <!-- 单击 -->
                <div class="action-item">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-7 h-7 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                                <i class="fas fa-hand-pointer text-green-600"></i>
                            </div>
                            <div>
                                <div class="font-medium text-gray-800">单击</div>
                                <div class="text-sm text-gray-500">开关切换</div>
                            </div>
                        </div>
                        <div class="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center">
                            <i class="fas fa-check text-green-600 text-sm"></i>
                        </div>
                    </div>
                </div>

                <!-- 双击 -->
                <div class="action-item">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-7 h-7 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                                <i class="fas fa-mouse text-blue-600"></i>
                            </div>
                            <div>
                                <div class="font-medium text-gray-800">双击</div>
                                <div class="text-sm text-gray-500">打开</div>
                            </div>
                        </div>
                        <div class="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center">
                            <i class="fas fa-check text-blue-600 text-sm"></i>
                        </div>
                    </div>
                </div>

                <!-- 长按 -->
                <div class="action-item">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-7 h-7 bg-purple-100 rounded-lg flex items-center justify-center mr-3">
                                <i class="fas fa-hand-paper text-purple-600"></i>
                            </div>
                            <div>
                                <div class="font-medium text-gray-800">长按</div>
                                <div class="text-sm text-gray-500">点击展开选择动作</div>
                            </div>
                        </div>
                        <i class="fas fa-chevron-right text-gray-400"></i>
                    </div>
                </div>

                <!-- 旋转 -->
                <div class="action-item">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-7 h-7 bg-orange-100 rounded-lg flex items-center justify-center mr-3">
                                <i class="fas fa-redo text-orange-600"></i>
                            </div>
                            <div>
                                <div class="font-medium text-gray-800">旋转</div>
                                <div class="text-sm text-gray-500">仅支持可调节动作</div>
                            </div>
                        </div>
                        <i class="fas fa-chevron-down text-gray-400"></i>
                    </div>
                </div>

                <!-- 旋转动作选择区域 -->
                <div class="ml-4 mt-1 p-2 bg-gray-50 rounded-xl space-y-1">
                    <!-- 亮度调节 -->
                    <div class="action-item selected">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="w-6 h-6 bg-yellow-100 rounded-lg flex items-center justify-center mr-3">
                                    <i class="fas fa-sun text-yellow-600 text-xs"></i>
                                </div>
                                <div>
                                    <div class="font-medium text-gray-800 text-sm">亮度调节</div>
                                    <div class="text-xs text-gray-500">调节灯光亮度 0-100%</div>
                                </div>
                            </div>
                            <i class="fas fa-check text-blue-600"></i>
                        </div>
                    </div>

                    <!-- 色温调节 -->
                    <div class="action-item">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="w-6 h-6 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                                    <i class="fas fa-thermometer-half text-blue-600 text-xs"></i>
                                </div>
                                <div>
                                    <div class="font-medium text-gray-800 text-sm">色温调节</div>
                                    <div class="text-xs text-gray-500">调节色温 2700K-6500K</div>
                                </div>
                            </div>
                            <i class="fas fa-chevron-right text-gray-400"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 完成按钮 -->
        <div class="w-full bg-blue-500 text-white py-4 rounded-xl font-medium text-base text-center mt-12">
            完成
        </div>
    </div>

    <!-- iOS底部指示器 -->
    <div class="fixed bottom-0 left-0 right-0 flex justify-center pt-4 pb-2 bg-transparent">
        <div class="w-32 h-1 bg-black rounded-full"></div>
    </div>
</body>
</html>
