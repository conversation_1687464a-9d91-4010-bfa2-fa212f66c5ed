<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>操作配置</title>
    <link href="https://unpkg.com/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            background: linear-gradient(180deg, #e6f3ff 0%, #f0f8ff 50%, #e8f4f8 100%);
        }
        .config-card {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        .device-item {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border-radius: 12px;
            padding: 12px;
            margin: 6px 0;
            border: 2px solid transparent;
        }
        .device-item.selected {
            border-color: #3b82f6;
            background: rgba(59, 130, 246, 0.1);
        }
        .action-item {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border-radius: 12px;
            padding: 12px;
            margin: 6px 0;
            border: 2px solid transparent;
        }
        .action-item.selected {
            border-color: #3b82f6;
            background: rgba(59, 130, 246, 0.1);
        }

    </style>
</head>
<body class="h-screen">
    <!-- iOS状态栏 -->
    <div class="fixed top-0 left-0 right-0 text-black text-sm px-4 py-2 flex justify-between items-center z-50"
        style="background: linear-gradient(180deg, #e6f3ff 0%, #f0f8ff 100%);">
        <div class="font-semibold">9:41</div>
        <div class="flex items-center space-x-1">
            <i class="fas fa-signal text-xs"></i>
            <i class="fas fa-wifi text-xs"></i>
            <i class="fas fa-battery-full text-xs"></i>
        </div>
    </div>

    <!-- 顶部导航 -->
    <div class="fixed top-10 left-0 right-0 bg-transparent px-4 py-3 z-40">
        <div class="flex items-center justify-between">
            <button class="flex items-center">
                <i class="fas fa-chevron-left text-gray-600 text-lg"></i>
            </button>
            <h1 class="text-lg font-semibold text-gray-800">上键配置</h1>
            <div class="w-8"></div>
        </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="pt-24 pb-20 px-4">
        <!-- Tab切换和内容区域 -->
        <div class="config-card rounded-2xl p-4 mb-6">
            <!-- Tab标签 -->
            <div class="flex bg-gray-100 rounded-xl p-1 mb-6">
                <div class="flex-1 text-center py-2 px-4 bg-blue-500 text-white rounded-lg font-medium text-sm">
                    设备控制
                </div>
                <div class="flex-1 text-center py-2 px-4 text-gray-600 font-medium text-sm">
                    场景触发
                </div>
            </div>



            <!-- 设备选择区域 -->
            <div class="mb-6">
                <h3 class="text-md font-semibold text-gray-800 mb-4">选择设备</h3>

                <!-- 房间选择 -->
                <div class="flex space-x-2 mb-4 overflow-x-auto">
                    <div class="bg-blue-500 text-white px-4 py-2 rounded-lg text-sm font-medium whitespace-nowrap">
                        全部
                    </div>
                    <div class="bg-gray-100 text-gray-600 px-4 py-2 rounded-lg text-sm font-medium whitespace-nowrap">
                        客厅
                    </div>
                    <div class="bg-gray-100 text-gray-600 px-4 py-2 rounded-lg text-sm font-medium whitespace-nowrap">
                        卧室
                    </div>
                </div>

            <div class="space-y-2">
                <!-- 客厅设备 -->
                <div class="device-item selected">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center mr-3">
                                <i class="fas fa-lightbulb text-yellow-600"></i>
                            </div>
                            <div>
                                <div class="font-medium text-gray-800">客厅灯</div>
                                <div class="text-sm text-gray-500">客厅 · 智能LED灯</div>
                            </div>
                        </div>
                        <i class="fas fa-check text-blue-600"></i>
                    </div>
                </div>
                <div class="device-item">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-gray-100 rounded-lg flex items-center justify-center mr-3">
                                <i class="fas fa-tv text-gray-600"></i>
                            </div>
                            <div>
                                <div class="font-medium text-gray-800">客厅电视</div>
                                <div class="text-sm text-gray-500">客厅 · 智能电视</div>
                            </div>
                        </div>
                        <i class="fas fa-chevron-right text-gray-400"></i>
                    </div>
                </div>

                <div class="device-item">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                                <i class="fas fa-window-maximize text-blue-600"></i>
                            </div>
                            <div>
                                <div class="font-medium text-gray-800">卧室窗帘</div>
                                <div class="text-sm text-gray-500">卧室 · 电动窗帘</div>
                            </div>
                        </div>
                        <i class="fas fa-chevron-right text-gray-400"></i>
                    </div>
                </div>


            </div>
            </div>


        </div>

    <!-- 底部按钮 -->
    <div class="fixed bottom-8 left-0 right-0 bg-transparent px-4">
        <button class="w-full bg-blue-500 text-white py-4 rounded-xl font-medium text-base">
            下一步
        </button>
    </div>
    </div>

    <!-- iOS底部指示器 -->
    <div class="fixed bottom-0 left-0 right-0 flex justify-center pt-4 pb-2 bg-transparent">
        <div class="w-32 h-1 bg-black rounded-full"></div>
    </div>
</body>
</html>
