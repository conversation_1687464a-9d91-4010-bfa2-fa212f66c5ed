<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>上键配置</title>
    <link href="https://unpkg.com/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            background: linear-gradient(180deg, #e6f3ff 0%, #f0f8ff 50%, #e8f4f8 100%);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        .action-card {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        .action-item {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border-radius: 8px;
            padding: 8px;
            margin: 3px 0;
            border: 2px solid transparent;
        }
        .action-item.disabled {
            opacity: 0.6;
            background: rgba(255, 255, 255, 0.5);
        }
    </style>
</head>
<body class="h-screen">
    <!-- iOS状态栏 -->
    <div class="fixed top-0 left-0 right-0 text-black text-sm px-4 py-2 flex justify-between items-center z-50"
        style="background: linear-gradient(180deg, #e6f3ff 0%, #f0f8ff 100%);">
        <div class="font-semibold">9:41</div>
        <div class="flex items-center space-x-1">
            <i class="fas fa-signal text-xs"></i>
            <i class="fas fa-wifi text-xs"></i>
            <i class="fas fa-battery-full text-xs"></i>
        </div>
    </div>

    <!-- 顶部导航 -->
    <div class="fixed top-10 left-0 right-0 bg-transparent px-4 py-3 z-40">
        <div class="flex items-center justify-between">
            <button class="flex items-center">
                <i class="fas fa-chevron-left text-gray-600 text-lg"></i>
            </button>
            <h1 class="text-lg font-semibold text-gray-800">上键配置</h1>
            <div class="w-6"></div>
        </div>
    </div>

    <!-- 主内容区域 -->
    <div class="pt-24 pb-20 px-4">
        <!-- 配置完成状态 -->
        <div class="mb-4">
            <div class="text-center">
                <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-2">
                    <i class="fas fa-check text-green-600 text-lg"></i>
                </div>
                <h2 class="text-lg font-semibold text-gray-800 mb-1">配置完成</h2>
            </div>
        </div>

        <!-- 当前配置显示 -->
        <div class="action-card rounded-2xl p-3 mb-4">
            <div class="flex items-center mb-4">
                <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                    <i class="fas fa-chevron-up text-blue-600"></i>
                </div>
                <div>
                    <div class="font-semibold text-gray-800">上键</div>
                    <div class="text-sm text-gray-500">已选设备：客厅灯</div>
                </div>
            </div>
        </div>

        <!-- 上键配置说明 -->
        <div class="action-card rounded-2xl p-3 mb-4">
            <h3 class="text-sm font-semibold text-gray-800 mb-3">按键功能说明</h3>

            <div class="space-y-2">
                <!-- 单击 -->
                <div class="action-item">
                    <div class="flex items-center">
                        <div class="w-7 h-7 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                            <i class="fas fa-hand-pointer text-green-600 text-sm"></i>
                        </div>
                        <div>
                            <div class="font-medium text-gray-800 text-sm">单击</div>
                            <div class="text-xs text-gray-500">开关切换</div>
                        </div>
                    </div>
                </div>

                <!-- 双击 -->
                <div class="action-item">
                    <div class="flex items-center">
                        <div class="w-7 h-7 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                            <i class="fas fa-mouse text-blue-600 text-sm"></i>
                        </div>
                        <div>
                            <div class="font-medium text-gray-800 text-sm">双击</div>
                            <div class="text-xs text-gray-500">切换旋转控制模式</div>
                        </div>
                    </div>
                </div>

                <!-- 长按 -->
                <div class="action-item disabled">
                    <div class="flex items-center">
                        <div class="w-7 h-7 bg-gray-100 rounded-lg flex items-center justify-center mr-3">
                            <i class="fas fa-hand-paper text-gray-400 text-sm"></i>
                        </div>
                        <div>
                            <div class="font-medium text-gray-800 text-sm">长按</div>
                            <div class="text-xs text-gray-500">未配置</div>
                        </div>
                    </div>
                </div>

                <!-- 旋转 -->
                <div class="action-item">
                    <div class="flex items-center">
                        <div class="w-7 h-7 bg-orange-100 rounded-lg flex items-center justify-center mr-3">
                            <i class="fas fa-redo text-orange-600 text-sm"></i>
                        </div>
                        <div>
                            <div class="font-medium text-gray-800 text-sm">旋转</div>
                            <div class="text-xs text-gray-500">亮度调节、色温调节</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 高级玩法入口 -->
        <div class="text-center mt-6 mb-4">
            <p class="text-gray-500 text-sm mb-2">需要更多自定义功能？</p>
            <a href="#" class="text-blue-600 font-medium text-sm flex items-center justify-center">
                <i class="fas fa-cogs text-blue-600 text-xs mr-1"></i>
                高级设置
            </a>
        </div>
        </div>
    </div>

    <!-- 底部按钮 -->
    <div class="fixed bottom-8 left-0 right-0 bg-transparent px-4">
        <button class="w-full bg-blue-500 text-white py-4 rounded-xl font-medium text-base">
            完成
        </button>
    </div>

    <!-- iOS底部指示器 -->
    <div class="fixed bottom-0 left-0 right-0 flex justify-center pt-4 pb-2 bg-transparent">
        <div class="w-32 h-1 bg-black rounded-full"></div>
    </div>
</body>
</html>
