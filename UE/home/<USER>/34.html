<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>子设备添加-选择网关</title>
    <link href="https://unpkg.com/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            background: linear-gradient(180deg, #e6f3ff 0%, #f0f8ff 100%);
            overflow: hidden;
        }
        .gateway-card {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .gateway-card.selected {
            border-color: #3b82f6;
            background: rgba(59, 130, 246, 0.1);
        }
    </style>
</head>
<body class="h-screen">
    <!-- iOS状态栏 -->
    <div class="fixed top-0 left-0 right-0 text-black text-sm px-4 py-2 flex justify-between items-center z-50" style="background: linear-gradient(180deg, #e6f3ff 0%, #f0f8ff 100%);">
        <div class="font-semibold">
            9:41
        </div>
        <div class="flex items-center space-x-1">
            <i class="fas fa-signal text-xs"></i>
            <i class="fas fa-wifi text-xs"></i>
            <i class="fas fa-battery-full text-xs"></i>
        </div>
    </div>

    <!-- 主内容区域 -->
    <div class="pt-12 h-full flex flex-col">
        <!-- 顶部导航栏 -->
        <div class="fixed top-10 left-0 right-0 bg-transparent px-4 py-3 z-40">
            <div class="flex items-center justify-center relative">
                <button class="absolute left-0">
                    <i class="fas fa-chevron-left text-gray-600 text-lg"></i>
                </button>
                <h1 class="text-lg font-semibold text-gray-800">添加设备</h1>
            </div>
        </div>

        <!-- 内容区域 -->
        <div class="flex-1 px-4 py-2 pt-12 pb-24">
            <!-- 说明文字 -->
            <div class="mb-6">
                <p class="text-gray-600 text-sm">
                    请选择添加子设备的网关
                </p>
            </div>

            <!-- 网关列表 -->
            <div class="space-y-3">
                <!-- 5G CPE网关 -->
                <div class="gateway-card rounded-lg p-4 border border-gray-200 cursor-pointer">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                                <i class="fa-solid fa-charging-station text-blue-600 text-lg"></i>
                            </div>
                            <div>
                                <h3 class="font-medium text-gray-800">5G CPE P3</h3>
                                <p class="text-sm text-gray-500">客厅</p>
                            </div>
                        </div>
                        <i class="fas fa-chevron-right text-gray-400 text-lg"></i>
                    </div>
                </div>

                <!-- 书房5G CPE -->
                <div class="gateway-card rounded-lg p-4 border border-gray-200 cursor-pointer">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                                <i class="fa-solid fa-charging-station text-green-600 text-lg"></i>
                            </div>
                            <div>
                                <h3 class="font-medium text-gray-800">5G CPE P3</h3>
                                <p class="text-sm text-gray-500">书房</p>
                            </div>
                        </div>
                        <i class="fas fa-chevron-right text-gray-400 text-lg"></i>
                    </div>
                </div>
            </div>

        </div>
    </div>

    <!-- 底部按钮 -->
    <div class="fixed bottom-8 left-0 right-0 px-4 bg-transparent">
        <button class="w-full bg-gray-300 text-gray-500 py-4 rounded-xl font-medium text-base cursor-not-allowed">
            下一步
        </button>
    </div>

    <!-- iOS底部指示器 -->
    <div class="fixed bottom-0 left-0 right-0 flex justify-center pt-4 pb-2 bg-transparent">
        <div class="w-32 h-1 bg-black rounded-full"></div>
    </div>
</body>
</html>
