<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能旋钮</title>
    <link href="https://unpkg.com/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            background: linear-gradient(180deg, #e6f3ff 0%, #f0f8ff 50%, #e8f4f8 100%);
        }
        .device-card {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        .status-online {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        }
        .battery-indicator {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
        }

        /* 低电量告警样式 */
        .low-battery-alert {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            backdrop-filter: blur(10px);
        }

        /* 圆环旋转控制样式 */
        .rotation-ring {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 350px;
            height: 350px;
            border: 3px solid rgba(59, 130, 246, 0.3);
            border-radius: 50%;
        }

        .rotation-indicator {
            position: absolute;
            top: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 20px;
            height: 20px;
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            border-radius: 50%;
            box-shadow: 0 3px 12px rgba(59, 130, 246, 0.5);
            border: 2px solid white;
        }

        /* 新的功能入口样式 */
        .function-entry-group {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 8px;
        }

        .function-icon-circle {
            width: 64px;
            height: 64px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(0, 0, 0, 0.05);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.12);
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
        }

        .function-icon-circle:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .function-icon-circle i {
            font-size: 28px;
        }

        .function-entry-group span {
            font-size: 14px;
            font-weight: 600;
            color: #4b5563;
        }

        .entry-green i {
            color: #10b981;
        }

        .entry-purple i {
            color: #a855f7;
        }

        .entry-disabled {
            opacity: 0.5;
            pointer-events: none;
        }

        .entry-disabled .function-icon-circle {
            background: rgba(255, 255, 255, 0.5);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }

        .entry-disabled i {
            color: #9ca3af !important;
        }

        .entry-disabled span {
            color: #9ca3af !important;
        }

        /* 极简圆形控制器 */
        .minimal-controller {
            position: relative;
            width: 360px;
            height: 360px;
            margin: 0 auto;
        }

        .background-circle {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 320px;
            height: 320px;
            background: rgba(255, 255, 255, 0.9);
            border: 2px solid rgba(59, 130, 246, 0.2);
            border-radius: 50%;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        /* 旋转值显示 */
        .rotation-value {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            z-index: 10;
        }

        .rotation-value .value {
            font-size: 48px;
            font-weight: bold;
            color: #1f2937;
            line-height: 1;
        }

        .rotation-value .label {
            font-size: 14px;
            color: #6b7280;
            margin-top: 4px;
        }

        /* 设备信息组合 - 图标+文字 */
        .device-info {
            position: absolute;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .device-info i {
            font-size: 20px;
            color: #6b7280;
            margin-bottom: 6px;
        }

        .device-info span {
            font-size: 14px;
            color: #4b5563;
            font-weight: 600;
            white-space: nowrap;
        }

        .device-info.active i {
            color: #3b82f6;
            transform: scale(1.1);
        }

        .device-info.active span {
            color: #3b82f6;
            font-weight: 700;
        }

        .device-info:not(.active) {
            opacity: 0.6;
        }

        /* 设备信息位置 - 在圆形内部 */
        .info-up {
            top: 45px;
            left: 50%;
            transform: translateX(-50%);
        }

        .info-right {
            top: 50%;
            right: 45px;
            transform: translateY(-50%);
        }

        .info-down {
            bottom: 45px;
            left: 50%;
            transform: translateX(-50%);
        }

        .info-left {
            top: 50%;
            left: 45px;
            transform: translateY(-50%);
        }

        /* 状态信息栏图标调整 */
        .fas.fa-wifi,
        .fas.fa-battery-three-quarters {
            transform: translateY(2px);
            margin-right: -3px;
        }

        /* 配置页面样式 */
        .config-item {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border-radius: 12px;
            padding: 16px;
            margin: 0 16px 12px 16px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .config-list {
            margin: 0 16px;
        }

        .config-list-item {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            padding: 16px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .config-list-item:first-child {
            border-top-left-radius: 12px;
            border-top-right-radius: 12px;
        }

        .config-list-item:last-child {
            border-bottom-left-radius: 12px;
            border-bottom-right-radius: 12px;
            border-bottom: none;
        }
    </style>
</head>
<body class="h-screen">
    <!-- iOS状态栏 -->
    <div class="fixed top-0 left-0 right-0 text-black text-sm px-4 py-2 flex justify-between items-center z-50"
        style="background: linear-gradient(180deg, #e6f3ff 0%, #f0f8ff 100%);">
        <div class="font-semibold">
            9:41
        </div>
        <div class="flex items-center space-x-1">
            <i class="fas fa-signal text-xs"></i>
            <i class="fas fa-wifi text-xs"></i>
            <i class="fas fa-battery-full text-xs"></i>
        </div>
    </div>

    <!-- 顶部导航 -->
    <div class="fixed top-10 left-0 right-0 bg-transparent px-4 py-3 z-40">
        <div class="flex items-center justify-center relative">
            <button class="absolute left-0">
                <i class="fas fa-chevron-left text-gray-600 text-lg"></i>
            </button>
            <h1 class="text-lg font-semibold text-gray-800">智能旋钮</h1>
            <div class="absolute right-0">
                <i class="fas fa-ellipsis-h text-gray-600 text-lg"></i>
            </div>
        </div>
    </div>

    <!-- 低电量告警横条 -->
    <div class="fixed top-20 left-0 right-0 z-30">
        <div class="low-battery-alert p-2 flex items-center justify-between">
            <div class="flex items-center">
                <div class="w-6 h-6 bg-white bg-opacity-20 rounded flex items-center justify-center mr-2">
                    <i class="fas fa-battery-quarter text-white text-xs"></i>
                </div>
                <div class="text-white font-medium text-xs">电量不足 15%，请及时更换电池</div>
            </div>
            <button class="w-5 h-5 flex items-center justify-center">
                <i class="fas fa-times text-white text-xs"></i>
            </button>
        </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="pt-36 pb-20 px-4">
        <!-- 1. 状态信息栏 -->
        <div class="flex justify-between items-center mb-8 w-full max-w-sm mx-auto">
            <div class="flex items-center gap-2 bg-white bg-opacity-90 backdrop-filter backdrop-blur-sm rounded-full px-4 py-2 shadow-sm">
                <i class="fas fa-wifi text-green-500"></i>
                <span class="text-sm font-semibold text-gray-700">在线</span>
            </div>
            <div class="flex items-center gap-2 bg-white bg-opacity-90 backdrop-filter backdrop-blur-sm rounded-full px-4 py-2 shadow-sm">
                <i class="fas fa-battery-quarter text-red-500"></i>
                <span class="text-sm font-semibold text-red-600">15%</span>
            </div>
        </div>

        <!-- 2. 圆形控制区域 -->
        <div class="flex justify-center mb-8">
            <div class="minimal-controller">
                <!-- 背景圆形 -->
                <div class="background-circle"></div>

                <!-- 旋转控制环 -->
                <div class="rotation-ring">
                    <div class="rotation-indicator"></div>
                </div>

                <!-- 旋转值显示 -->
                <div class="rotation-value">
                    <div class="value">50</div>
                    <div class="label">亮度</div>
                </div>

                <!-- 设备信息组合 - 图标+文字 -->
                <div class="device-info info-up active">
                    <i class="fas fa-lightbulb"></i>
                    <span>客厅灯</span>
                </div>
                <div class="device-info info-right">
                    <i class="fas fa-window-maximize"></i>
                    <span>阳台窗帘</span>
                </div>
                <div class="device-info info-down">
                    <i class="fas fa-home text-blue-600"></i>
                    <span>回家模式</span>
                </div>
                <div class="device-info info-left">
                    <i class="fas fa-plus-circle"></i>
                    <span>未设置</span>
                </div>
            </div>
        </div>

        <!-- 功能入口 -->
        <div class="fixed bottom-24 left-0 right-0 px-4">
            <div class="flex items-start justify-between w-full max-w-sm mx-auto">
                <!-- 高级设置 -->
                <div class="function-entry-group">
                    <div class="function-icon-circle entry-purple">
                        <i class="fas fa-magic"></i>
                    </div>
                    <span>高级设置</span>
                </div>
                <!-- 基础设置 -->
                <div class="function-entry-group">
                    <div class="function-icon-circle entry-green">
                        <i class="fas fa-sliders-h"></i>
                    </div>
                    <span>基础设置</span>
                </div>
            </div>
        </div>
    </div>

    <!-- iOS底部指示器 -->
    <div class="fixed bottom-0 left-0 right-0 flex justify-center pt-4 pb-2 bg-transparent">
        <div class="w-32 h-1 bg-black rounded-full"></div>
    </div>

</body>
</html>
