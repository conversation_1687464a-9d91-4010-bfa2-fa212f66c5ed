<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>单击操作配置</title>
    <link href="https://unpkg.com/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            background: linear-gradient(180deg, #e6f3ff 0%, #f0f8ff 50%, #e8f4f8 100%);
        }
        .setting-card {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

    </style>
</head>
<body class="h-screen">
<!-- iOS状态栏 -->
<div class="fixed top-0 left-0 right-0 text-black text-sm px-4 py-2 flex justify-between items-center z-50"
     style="background: linear-gradient(180deg, #e6f3ff 0%, #f0f8ff 100%);">
    <div class="font-semibold">9:41</div>
    <div class="flex items-center space-x-1">
        <i class="fas fa-signal text-xs"></i>
        <i class="fas fa-wifi text-xs"></i>
        <i class="fas fa-battery-full text-xs"></i>
    </div>
</div>

<!-- 顶部导航 -->
<div class="fixed top-10 left-0 right-0 bg-transparent px-4 py-3 z-40">
    <div class="flex items-center justify-center relative">
        <button class="absolute left-0">
            <i class="fas fa-chevron-left text-gray-600 text-lg"></i>
        </button>
        <h1 class="text-lg font-semibold text-gray-800">功能设置</h1>
    </div>
</div>

<!-- 主要内容区域 -->
<div class="pt-24 pb-8 px-4">
    <!-- 操作说明 -->
    <div class="mb-4">
        <div class="text-center">
            <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-2">
                <i class="fas fa-sliders-h text-green-600 text-lg"></i>
            </div>
            <h2 class="text-lg font-semibold text-gray-800 mb-1">快速设置</h2>
            <p class="text-sm text-gray-600">请选择要配置的按键</p>
        </div>
    </div>

    <!-- 按键选择区域 -->
    <div class="setting-card rounded-2xl p-4 mb-4">
        <h3 class="text-md font-semibold text-gray-800 mb-3">选择按键</h3>

        <div class="space-y-3">
            <!-- 上键 -->
            <div>
                <div class="flex items-center justify-between p-3 bg-gray-50 rounded-xl">
                    <div class="flex items-center">
                        <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                            <i class="fas fa-chevron-up text-blue-600"></i>
                        </div>
                        <div class="flex-1">
                            <div class="font-medium text-gray-800">上键</div>
                            <div class="text-sm text-gray-500">客厅灯</div>
                        </div>
                    </div>
                    <i class="fas fa-chevron-right text-gray-400"></i>
                </div>
            </div>

            <!-- 右键 -->
            <div>
                <div class="flex items-center justify-between p-3 bg-gray-50 rounded-xl">
                    <div class="flex items-center">
                        <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                            <i class="fas fa-chevron-right text-blue-600"></i>
                        </div>
                        <div>
                            <div class="font-medium text-gray-800">右键</div>
                            <div class="text-sm text-gray-500">未配置</div>
                        </div>
                    </div>
                    <i class="fas fa-chevron-right text-gray-400"></i>
                </div>
            </div>

            <!-- 下键 -->
            <div>
                <div class="flex items-center justify-between p-3 bg-gray-50 rounded-xl">
                    <div class="flex items-center">
                        <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                            <i class="fas fa-chevron-down text-blue-600"></i>
                        </div>
                        <div>
                            <div class="font-medium text-gray-800">下键</div>
                            <div class="text-sm text-gray-500">未配置</div>
                        </div>
                    </div>
                    <i class="fas fa-chevron-right text-gray-400"></i>
                </div>
            </div>

            <!-- 左键 -->
            <div>
                <div class="flex items-center justify-between p-3 bg-gray-50 rounded-xl">
                    <div class="flex items-center">
                        <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                            <i class="fas fa-chevron-left text-blue-600"></i>
                        </div>
                        <div>
                            <div class="font-medium text-gray-800">左键</div>
                            <div class="text-sm text-gray-500">未配置</div>
                        </div>
                    </div>
                    <i class="fas fa-chevron-right text-gray-400"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- 操作提示 -->
    <div class="mt-4 mb-2 px-2">
        <div class="flex items-start">
            <div class="flex-shrink-0 mr-3">
                <i class="fas fa-lightbulb text-blue-500"></i>
            </div>
            <div class="flex-1">
                <p class="text-blue-600 font-medium text-sm mb-1">配置说明</p>
                <p class="text-gray-600 text-sm leading-relaxed">
                    选择要配置的按键，然后为其设置设备控制或场景操作
                </p>
            </div>
        </div>
    </div>
</div>

<!-- iOS底部指示器 -->
<div class="fixed bottom-0 left-0 right-0 flex justify-center pt-4 pb-2 bg-transparent">
    <div class="w-32 h-1 bg-black rounded-full"></div>
</div>
</body>
</html>
