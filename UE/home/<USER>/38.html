<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>子设备-配置向导</title>
    <link href="https://unpkg.com/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(180deg, #e6f3ff 0%, #f0f8ff 100%);
            overflow: hidden;
        }
        
        .device-illustration {
            width: 200px;
            height: 200px;
            margin: 0 auto;
            position: relative;
        }
        
        .device-base {
            width: 120px;
            height: 120px;
            background: linear-gradient(135deg, #4A5568 0%, #2D3748 100%);
            border-radius: 50%;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
        }
        
        .device-ring {
            width: 140px;
            height: 140px;
            border: 8px solid #E2E8F0;
            border-radius: 50%;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }

        .device-speaker {
            width: 8px;
            height: 8px;
            background: #2D3748;
            border-radius: 50%;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }
        
        .hand-pointer {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 28px;
            color: #F56565;
        }

        .device-button {
            width: 20px;
            height: 20px;
            background: #F56565;
            border-radius: 50%;
            position: absolute;
            top: 20px;
            right: 30px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }
    </style>
</head>
<body class="h-screen">
    <div class="flex flex-col h-full">
        <!-- iOS状态栏 -->
        <div class="fixed top-0 left-0 right-0 text-black text-sm px-4 py-2 flex justify-between items-center z-50" style="background: linear-gradient(180deg, #e6f3ff 0%, #f0f8ff 100%);">
            <div class="font-semibold">
                9:41
            </div>
            <div class="flex items-center space-x-1">
                <i class="fas fa-signal text-xs"></i>
                <i class="fas fa-wifi text-xs"></i>
                <i class="fas fa-battery-full text-xs"></i>
            </div>
        </div>

        <!-- 顶部导航栏 -->
        <div class="fixed top-10 left-0 right-0 bg-transparent px-4 py-3 z-40">
            <div class="flex items-center justify-center relative">
                <button class="absolute left-0">
                    <i class="fas fa-chevron-left text-gray-600 text-lg"></i>
                </button>
                <h1 class="text-lg font-semibold text-gray-800">配置向导</h1>
            </div>
        </div>

        <!-- 主内容区域 -->
        <div class="flex-1 px-4 pt-20 pb-32 overflow-y-auto">
            <div class="flex flex-col items-center justify-center min-h-full py-8">
                <!-- 产品标题 -->
                <div class="mb-12">
                    <div class="flex items-center justify-center mb-4">
                        <div class="w-10 h-10 bg-blue-500 rounded-xl flex items-center justify-center mr-4">
                            <i class="fas fa-cog text-white text-lg"></i>
                        </div>
                        <h2 class="text-2xl font-bold text-gray-800">智能旋钮</h2>
                    </div>
                </div>

                <!-- 设备插画 -->
                <div class="device-illustration mb-12">
                    <div class="device-ring"></div>
                    <div class="device-base">
                        <div class="device-button"></div>
                        <div class="device-speaker"></div>
                    </div>
                    <div class="hand-pointer">
                        <i class="fas fa-hand-pointer"></i>
                    </div>
                </div>

                <!-- 操作说明 -->
                <div class="mb-8 max-w-sm w-full px-2">
                    <div class="flex items-start">
                        <div class="flex-shrink-0 mr-3">
                            <i class="fas fa-info-circle text-blue-500 text-lg"></i>
                        </div>
                        <div class="flex-1">
                            <p class="text-blue-600 font-medium text-sm mb-2">配网操作</p>
                            <p class="text-gray-600 text-sm leading-relaxed">
                                长按"电池仓内的按键"5秒以上然后松开至所有按键LED闪烁即可进入配网状态
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 底部操作区域 -->
        <div class="fixed bottom-8 left-0 right-0 bg-transparent px-4">
            <!-- 完成确认 -->
            <div class="flex items-center mb-5">
                <div id="checkbox" class="w-6 h-6 border-2 border-gray-300 rounded-full mr-4 flex items-center justify-center">
                    <div id="checkmark" class="w-2.5 h-2.5 bg-transparent rounded-full"></div>
                </div>
                <span class="text-gray-600 text-base">已完成上述操作</span>
            </div>

            <!-- 下一步按钮 -->
            <button id="nextButton" class="w-full bg-gray-300 text-gray-500 py-4 rounded-xl font-medium text-base cursor-not-allowed" disabled>
                下一步
            </button>
        </div>

        <!-- iOS底部指示器 -->
        <div class="fixed bottom-0 left-0 right-0 flex justify-center pt-4 pb-2 bg-transparent">
            <div class="w-32 h-1 bg-black rounded-full"></div>
        </div>
    </div>


</body>
</html>
