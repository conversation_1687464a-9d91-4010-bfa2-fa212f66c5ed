<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>人体存在平板灯</title>
    <link href="https://unpkg.com/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            background: linear-gradient(180deg, #e6f3ff 0%, #f0f8ff 50%, #e8f4f8 100%);
        }
        .setting-item {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
        }
        .scene-mode.active {
            background: #3b82f6;
            color: white;
        }
        .mode-card {
            background-size: cover;
            background-position: center;
            position: relative;
            overflow: hidden;
            min-width: 120px;
            flex-shrink: 0;
        }
        .mode-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.2);
            z-index: 1;
        }
        .mode-card .content {
            position: relative;
            z-index: 2;
        }
        .moonlight-bg {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }
        .bright-bg {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
        }
        .custom-bg {
            background: linear-gradient(135deg, #e0e0e0 0%, #c0c0c0 100%);
        }
        .mode-scroll {
            overflow-x: auto;
            scrollbar-width: none;
            -ms-overflow-style: none;
        }
        .mode-scroll::-webkit-scrollbar {
            display: none;
        }
    </style>
</head>
<body class="h-screen">
    <!-- iOS状态栏 -->
    <div class="fixed top-0 left-0 right-0 text-black text-sm px-4 py-2 flex justify-between items-center z-50"
        style="background: linear-gradient(180deg, #e6f3ff 0%, #f0f8ff 100%);">
        <div class="font-semibold">9:41</div>
        <div class="flex items-center space-x-1">
            <i class="fas fa-signal text-xs"></i>
            <i class="fas fa-wifi text-xs"></i>
            <i class="fas fa-battery-full text-xs"></i>
        </div>
    </div>

    <!-- 顶部导航 -->
    <div class="fixed top-10 left-0 right-0 bg-transparent px-4 py-3 z-40">
        <div class="flex items-center justify-center relative">
            <div class="absolute left-0">
                <i class="fas fa-chevron-left text-gray-600 text-lg"></i>
            </div>
            <h1 class="text-lg font-semibold text-gray-800">人体存在平板灯</h1>
            <div class="absolute right-0">
                <i class="fas fa-ellipsis-h text-gray-600 text-lg"></i>
            </div>
        </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="pt-24 pb-20">
        <!-- 设备控制区域 -->
        <div class="mx-4 mb-4">
            <div class="setting-item rounded-xl p-4">
                <!-- 设备状态 -->
                <div class="flex items-center justify-between mb-4">
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-yellow-100 rounded-xl flex items-center justify-center mr-3">
                            <i class="fas fa-lightbulb text-yellow-600 text-lg"></i>
                        </div>
                        <div>
                            <h2 class="text-base font-semibold text-gray-800">客厅平板灯</h2>
                            <div class="flex items-center mt-1">
                                <div class="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                                <span class="text-xs text-gray-500">在线</span>
                            </div>
                        </div>
                    </div>
                    <div class="relative inline-block w-12 h-6">
                        <div class="block w-full h-full bg-blue-500 rounded-full">
                            <span class="absolute left-1 top-1 w-4 h-4 bg-white rounded-full transform translate-x-6"></span>
                        </div>
                    </div>
                </div>

                <!-- 情景模式 -->
                <div class="mb-4">
                    <h3 class="text-sm font-medium text-gray-800 mb-3">情景模式</h3>
                    <div class="mode-scroll">
                        <div class="flex space-x-3">
                            <!-- 月光模式 -->
                            <div class="mode-card moonlight-bg rounded-xl p-3 h-20 flex flex-col justify-end cursor-pointer">
                                <div class="content">
                                    <h4 class="text-white text-sm font-medium">月光</h4>
                                    <p class="text-white text-xs opacity-80">1%</p>
                                </div>
                            </div>

                            <!-- 明亮模式 -->
                            <div class="mode-card bright-bg rounded-xl p-3 h-20 flex flex-col justify-end cursor-pointer">
                                <div class="content">
                                    <h4 class="text-white text-sm font-medium">明亮</h4>
                                    <p class="text-white text-xs opacity-80">100%</p>
                                </div>
                            </div>

                            <!-- 添加自定义模式 -->
                            <div class="mode-card bg-gray-100 rounded-xl p-3 h-20 flex flex-col justify-center items-center cursor-pointer border-2 border-dashed border-gray-300">
                                <i class="fas fa-plus text-gray-400 text-lg mb-1"></i>
                                <span class="text-gray-500 text-xs">自定义</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 亮度控制 -->
                <div>
                    <div class="flex items-center justify-between mb-3">
                        <span class="text-sm font-medium text-gray-700">亮度调节</span>
                        <span class="text-base font-semibold text-blue-600">60%</span>
                    </div>
                    <div class="flex items-center space-x-3">
                        <i class="fas fa-sun text-yellow-500 text-sm"></i>
                        <div class="flex-1 h-2 bg-gray-200 rounded-full">
                            <div class="h-full bg-gradient-to-r from-yellow-400 to-yellow-600 rounded-full" style="width: 60%"></div>
                        </div>
                        <i class="fas fa-sun text-yellow-500 text-lg"></i>
                    </div>
                    <div class="flex justify-between text-xs text-gray-400 mt-2">
                        <span>1%</span>
                        <span>100%</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 快速控制 -->
        <div class="mx-4 mb-3">
            <div class="setting-item rounded-xl p-4">
                <h3 class="text-sm font-medium text-gray-800 mb-3">快速控制</h3>
                <div class="grid grid-cols-2 gap-3">
                    <!-- 雷达感应开关 -->
                    <div class="p-3 bg-blue-50 rounded-xl border border-blue-200 cursor-pointer">
                        <div class="flex items-center justify-between mb-2">
                            <div class="text-sm font-medium text-gray-800">雷达感应</div>
                            <div class="relative inline-block w-8 h-4">
                                <div class="block w-full h-full bg-blue-500 rounded-full">
                                    <span class="absolute left-0.5 top-0.5 w-3 h-3 bg-white rounded-full transform translate-x-4"></span>
                                </div>
                            </div>
                        </div>
                        <div class="text-left">
                            <div class="text-xs text-gray-500">详细设置</div>
                        </div>
                    </div>

                    <!-- 自动感应灯光 -->
                    <div class="p-3 bg-green-50 rounded-xl border border-green-200 cursor-pointer">
                        <div class="flex items-center justify-between mb-2">
                            <div class="text-sm font-medium text-gray-800">自动感应灯光</div>
                            <div class="relative inline-block w-8 h-4">
                                <div class="block w-full h-full bg-green-500 rounded-full">
                                    <span class="absolute left-0.5 top-0.5 w-3 h-3 bg-white rounded-full transform translate-x-4"></span>
                                </div>
                            </div>
                        </div>
                        <div class="text-left">
                            <div class="text-xs text-gray-500">详细设置</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 今日设备日志 -->
        <div class="mx-4 mb-4">
            <div class="setting-item rounded-xl p-4">
                <div class="flex items-center justify-between mb-3">
                    <h3 class="text-sm font-medium text-gray-800">今日日志</h3>
                    <span class="text-xs text-blue-600 cursor-pointer">查看全部</span>
                </div>

                <div class="space-y-2">
                    <!-- 日志记录项 -->
                    <div class="flex items-center justify-between p-2 bg-gray-50 rounded-lg">
                        <div class="flex items-center">
                            <div class="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                            <span class="text-xs text-gray-700">雷达检测到人体</span>
                        </div>
                        <span class="text-xs text-gray-500">14:32</span>
                    </div>

                    <div class="flex items-center justify-between p-2 bg-gray-50 rounded-lg">
                        <div class="flex items-center">
                            <div class="w-2 h-2 bg-red-500 rounded-full mr-2"></div>
                            <span class="text-xs text-gray-700">雷达检测无人</span>
                        </div>
                        <span class="text-xs text-gray-500">14:47</span>
                    </div>

                    <div class="flex items-center justify-between p-2 bg-gray-50 rounded-lg">
                        <div class="flex items-center">
                            <div class="w-2 h-2 bg-blue-500 rounded-full mr-2"></div>
                            <span class="text-xs text-gray-700">光照度低于阈值</span>
                        </div>
                        <span class="text-xs text-gray-500">13:15</span>
                    </div>

                    <div class="flex items-center justify-between p-2 bg-gray-50 rounded-lg">
                        <div class="flex items-center">
                            <div class="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                            <span class="text-xs text-gray-700">雷达检测到人体</span>
                        </div>
                        <span class="text-xs text-gray-500">11:45</span>
                    </div>
                </div>


            </div>
        </div>
    </div>

    <!-- iOS底部指示器 -->
    <div class="fixed bottom-0 left-0 right-0 flex justify-center pt-4 pb-2 bg-transparent">
        <div class="w-32 h-1 bg-black rounded-full"></div>
    </div>

</body>
</html>