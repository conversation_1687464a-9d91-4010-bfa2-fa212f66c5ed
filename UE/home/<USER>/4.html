<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>添加设备</title>
    <link href="https://unpkg.com/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            background: linear-gradient(180deg, #e6f3ff 0%, #f0f8ff 100%);
            overflow: hidden;
        }
    </style>
</head>
<body class="h-screen">
    <!-- iOS状态栏 -->
    <div class="fixed top-0 left-0 right-0 text-black text-sm px-4 py-2 flex justify-between items-center z-50" style="background: linear-gradient(180deg, #e6f3ff 0%, #f0f8ff 100%);">
        <div class="font-semibold">
            9:41
        </div>
        <div class="flex items-center space-x-1">
            <i class="fas fa-signal text-xs"></i>
            <i class="fas fa-wifi text-xs"></i>
            <i class="fas fa-battery-full text-xs"></i>
        </div>
    </div>

    <!-- 顶部导航 -->
    <div class="fixed top-10 left-0 right-0 bg-transparent px-4 py-3 z-40">
        <div class="flex items-center justify-center relative">
            <button class="absolute left-0">
                <i class="fas fa-chevron-left text-gray-600 text-lg"></i>
            </button>
            <h1 class="text-lg font-semibold text-gray-800">添加设备</h1>
        </div>
    </div>

    <!-- 主内容区域 -->
    <div class="flex flex-col h-full pt-24 pb-8">
        <!-- 雷达扫描区域 -->
        <div class="flex justify-center items-center flex-1">
            <div class="relative">
                <!-- 外圈雷达 -->
                <div class="w-64 h-64 rounded-full border-2 border-blue-200"></div>
                <!-- 中圈雷达 -->
                <div class="absolute top-8 left-8 w-48 h-48 rounded-full border-2 border-blue-300"></div>
                <!-- 内圈雷达 -->
                <div class="absolute top-16 left-16 w-32 h-32 rounded-full border-2 border-blue-400"></div>

                <!-- 中心点 -->
                <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                    <div class="w-4 h-4 bg-blue-500 rounded-full"></div>
                </div>
            </div>
        </div>

        <!-- 扫描状态文字 -->
        <div class="text-center mb-6">
            <p class="text-lg text-gray-700 mb-2">正在发现附近的可添加设备...</p>
            <button class="text-blue-500 text-sm">
                长时间搜索不到设备？
            </button>
        </div>

        <!-- 帮助提示 -->
        <div class="mx-4 mb-6 px-2">
            <div class="flex items-start">
                <div class="flex-shrink-0 mr-3">
                    <i class="fas fa-question-circle text-blue-500 text-lg"></i>
                </div>
                <div class="flex-1">
                    <p class="text-blue-600 font-medium text-sm mb-1">长时间搜索不到设备？</p>
                    <p class="text-gray-600 text-sm leading-relaxed">
                        请确保设备已开机并处于配网模式，或尝试重新搜索
                    </p>
                </div>
            </div>
        </div>

        <!-- 重新搜索按钮 -->
        <div class="mx-4 mb-8">
            <button class="w-full bg-blue-500 text-white py-4 rounded-xl font-medium text-base">
                重新搜索
            </button>
        </div>

        <!-- 底部添加方式 -->
        <div class="mx-4">
            <div class="grid grid-cols-2 gap-8">
                <!-- 扫码添加 -->
                <div class="text-center">
                    <div class="w-16 h-16 mx-auto mb-3 flex items-center justify-center">
                        <i class="fas fa-qrcode text-gray-600 text-3xl"></i>
                    </div>
                    <span class="text-gray-700 text-sm">扫码添加</span>
                </div>

                <!-- 按型号添加 -->
                <div class="text-center">
                    <div class="w-16 h-16 mx-auto mb-3 flex items-center justify-center">
                        <i class="fas fa-th text-gray-600 text-3xl"></i>
                    </div>
                    <span class="text-gray-700 text-sm">按型号添加</span>
                </div>
            </div>
        </div>
    </div>

    <!-- iOS底部指示器 -->
    <div class="fixed bottom-0 left-0 right-0 flex justify-center pt-4 pb-2 bg-transparent">
        <div class="w-32 h-1 bg-black rounded-full"></div>
    </div>
</body>
</html>
