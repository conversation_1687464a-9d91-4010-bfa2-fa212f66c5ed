<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>通电后状态</title>
    <link href="https://unpkg.com/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            background: linear-gradient(180deg, #e6f3ff 0%, #f0f8ff 50%, #e8f4f8 100%);
        }

        .setting-item {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
        }
    </style>
</head>

<body class="h-screen">
    <!-- iOS状态栏 -->
    <div class="fixed top-0 left-0 right-0 text-black text-sm px-4 py-2 flex justify-between items-center z-50"
        style="background: linear-gradient(180deg, #e6f3ff 0%, #f0f8ff 100%);">
        <div class="font-semibold">9:41</div>
        <div class="flex items-center space-x-1">
            <i class="fas fa-signal text-xs"></i>
            <i class="fas fa-wifi text-xs"></i>
            <i class="fas fa-battery-full text-xs"></i>
        </div>
    </div>

    <!-- 顶部导航 -->
    <div class="fixed top-10 left-0 right-0 bg-transparent px-4 py-3 z-40">
        <div class="flex items-center justify-center relative">
            <div class="absolute left-0">
                <i class="fas fa-chevron-left text-gray-600 text-lg"></i>
            </div>
            <h1 class="text-lg font-semibold text-gray-800">通电后状态</h1>
        </div>
    </div>

    <!-- 主内容区域 -->
    <div class="pt-24 pb-20">
        <!-- 通电后状态区域 -->
        <div class="mx-4 mt-4">
            <!-- 通电后状态 -->
            <div class="setting-item rounded-xl p-4">
                <div class="mb-4">
                    <span class="text-gray-800 font-medium">通电后状态</span>
                    <div class="text-sm text-gray-500">选择灯具通电后的默认行为</div>
                </div>

                <div class="space-y-3">
                    <!-- 灯光记忆 - 选中状态 -->
                    <div class="flex items-center p-3 bg-blue-50 rounded-lg border-2 border-blue-200">
                        <div class="w-4 h-4 bg-blue-500 rounded-full mr-3 flex items-center justify-center">
                            <div class="w-2 h-2 bg-white rounded-full"></div>
                        </div>
                        <div>
                            <span class="text-gray-800 font-medium">灯光记忆</span>
                            <div class="text-sm text-gray-500">保存断电前灯光状态</div>
                        </div>
                    </div>

                    <!-- 默认开灯 - 未选中状态 -->
                    <div class="flex items-center p-3 bg-gray-50 rounded-lg border border-gray-200">
                        <div class="w-4 h-4 bg-gray-300 rounded-full mr-3 flex items-center justify-center">
                        </div>
                        <div>
                            <span class="text-gray-800 font-medium">默认开灯</span>
                            <div class="text-sm text-gray-500">每次上电灯具都会默认开灯</div>
                        </div>
                    </div>

                    <!-- 默认关灯 - 未选中状态 -->
                    <div class="flex items-center p-3 bg-gray-50 rounded-lg border border-gray-200">
                        <div class="w-4 h-4 bg-gray-300 rounded-full mr-3 flex items-center justify-center">
                        </div>
                        <div>
                            <span class="text-gray-800 font-medium">默认关灯</span>
                            <div class="text-sm text-gray-500">每次上电灯具都会默认关灯</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- iOS底部指示器 -->
    <div class="fixed bottom-0 left-0 right-0 flex justify-center pt-4 pb-2 bg-transparent">
        <div class="w-32 h-1 bg-black rounded-full"></div>
    </div>
</body>

</html>