<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设备日志</title>
    <link href="https://unpkg.com/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            background: linear-gradient(180deg, #e6f3ff 0%, #f0f8ff 50%, #e8f4f8 100%);
        }

        .setting-card {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .record-item {
            transition: all 0.3s ease;
        }

        .record-item:hover {
            background: rgba(59, 130, 246, 0.05);
        }

        .status-detected {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        }

        .status-left {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
        }

        .chart-container {
            height: 200px;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        }
    </style>
</head>

<body class="h-screen">
    <!-- iOS状态栏 -->
    <div class="fixed top-0 left-0 right-0 text-black text-sm px-4 py-2 flex justify-between items-center z-50"
        style="background: linear-gradient(180deg, #e6f3ff 0%, #f0f8ff 100%);">
        <div class="font-semibold">9:41</div>
        <div class="flex items-center space-x-1">
            <i class="fas fa-signal text-xs"></i>
            <i class="fas fa-wifi text-xs"></i>
            <i class="fas fa-battery-full text-xs"></i>
        </div>
    </div>

    <!-- 顶部导航 -->
    <div class="fixed top-10 left-0 right-0 bg-transparent px-4 py-3 z-40">
        <div class="flex items-center justify-center relative">
            <div class="absolute left-0">
                <i class="fas fa-chevron-left text-gray-600 text-lg"></i>
            </div>
            <h1 class="text-lg font-semibold text-gray-800">设备日志</h1>
        </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="pt-24 pb-20 px-4">
        <!-- Tab切换和内容区域 -->
        <div>
            <!-- Tab标签 -->
            <div class="flex bg-gray-100 rounded-xl p-1 mb-6">
                <div class="flex-1 text-center py-2 px-4 bg-blue-500 text-white rounded-lg font-medium text-sm">
                    雷达日志
                </div>
                <div class="flex-1 text-center py-2 px-4 text-gray-600 font-medium text-sm">
                    光照度日志
                </div>
            </div>



            <!-- 时间筛选 -->
            <div class="mb-6">
                <div class="flex space-x-2">
                    <div class="flex-1 py-2 px-4 bg-blue-600 text-white rounded-xl text-sm font-medium text-center">今日</div>
                    <div class="flex-1 py-2 px-4 bg-gray-100 text-gray-600 rounded-xl text-sm font-medium text-center">昨日</div>
                    <div class="flex-1 py-2 px-4 bg-gray-100 text-gray-600 rounded-xl text-sm font-medium text-center">本周</div>
                    <div class="flex-1 py-2 px-4 bg-gray-100 text-gray-600 rounded-xl text-sm font-medium text-center">本月</div>
                </div>
            </div>

            <!-- 雷达日志详细记录 -->
            <div>
                <div class="space-y-3">
                    <!-- 记录项 -->
                    <div class="record-item p-4 rounded-xl border border-gray-200">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="w-3 h-3 status-detected rounded-full mr-3"></div>
                                <div>
                                    <div class="font-medium text-gray-800">雷达检测到人体</div>
                                    <div class="text-sm text-gray-500">14:32:15</div>
                                </div>
                            </div>
                            <div class="text-right">
                                <div class="text-sm text-green-600">有人状态</div>
                            </div>
                        </div>
                    </div>

                    <div class="record-item p-4 rounded-xl border border-gray-200">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="w-3 h-3 status-left rounded-full mr-3"></div>
                                <div>
                                    <div class="font-medium text-gray-800">雷达检测无人</div>
                                    <div class="text-sm text-gray-500">14:47:23</div>
                                </div>
                            </div>
                            <div class="text-right">
                                <div class="text-sm text-orange-600">无人状态</div>
                            </div>
                        </div>
                    </div>

                    <div class="record-item p-4 rounded-xl border border-gray-200">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="w-3 h-3 status-detected rounded-full mr-3"></div>
                                <div>
                                    <div class="font-medium text-gray-800">雷达检测到人体</div>
                                    <div class="text-sm text-gray-500">13:15:42</div>
                                </div>
                            </div>
                            <div class="text-right">
                                <div class="text-sm text-green-600">有人状态</div>
                            </div>
                        </div>
                    </div>

                    <div class="record-item p-4 rounded-xl border border-gray-200">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="w-3 h-3 status-left rounded-full mr-3"></div>
                                <div>
                                    <div class="font-medium text-gray-800">雷达检测无人</div>
                                    <div class="text-sm text-gray-500">13:28:16</div>
                                </div>
                            </div>
                            <div class="text-right">
                                <div class="text-sm text-orange-600">无人状态</div>
                            </div>
                        </div>
                    </div>

                    <div class="record-item p-4 rounded-xl border border-gray-200">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="w-3 h-3 status-detected rounded-full mr-3"></div>
                                <div>
                                    <div class="font-medium text-gray-800">雷达检测到人体</div>
                                    <div class="text-sm text-gray-500">11:45:33</div>
                                </div>
                            </div>
                            <div class="text-right">
                                <div class="text-sm text-green-600">有人状态</div>
                            </div>
                        </div>
                    </div>

                    <div class="record-item p-4 rounded-xl border border-gray-200">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="w-3 h-3 status-detected rounded-full mr-3"></div>
                                <div>
                                    <div class="font-medium text-gray-800">雷达检测到人体</div>
                                    <div class="text-sm text-gray-500">09:22:18</div>
                                </div>
                            </div>
                            <div class="text-right">
                                <div class="text-sm text-green-600">有人状态</div>
                            </div>
                        </div>
                    </div>
                </div>


            </div>
        </div>
    </div>

    <!-- iOS底部指示器 -->
    <div class="fixed bottom-0 left-0 right-0 flex justify-center pt-4 pb-2 bg-transparent">
        <div class="w-32 h-1 bg-black rounded-full"></div>
    </div>

</body>

</html>