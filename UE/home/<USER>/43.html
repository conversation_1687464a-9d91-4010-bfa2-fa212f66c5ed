<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>感应设置</title>
    <link href="https://unpkg.com/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            background: linear-gradient(180deg, #e6f3ff 0%, #f0f8ff 50%, #e8f4f8 100%);
        }

        .setting-card {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .sensitivity-option.active {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            color: white;
        }
    </style>
</head>

<body class="h-screen">
    <!-- iOS状态栏 -->
    <div class="fixed top-0 left-0 right-0 text-black text-sm px-4 py-2 flex justify-between items-center z-50"
        style="background: linear-gradient(180deg, #e6f3ff 0%, #f0f8ff 100%);">
        <div class="font-semibold">9:41</div>
        <div class="flex items-center space-x-1">
            <i class="fas fa-signal text-xs"></i>
            <i class="fas fa-wifi text-xs"></i>
            <i class="fas fa-battery-full text-xs"></i>
        </div>
    </div>

    <!-- 顶部导航 -->
    <div class="fixed top-10 left-0 right-0 bg-transparent px-4 py-3 z-40">
        <div class="flex items-center justify-center relative">
            <div class="absolute left-0">
                <i class="fas fa-chevron-left text-gray-600 text-lg"></i>
            </div>
            <h1 class="text-lg font-semibold text-gray-800">感应设置</h1>
        </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="pt-24 pb-20 px-4">
        <!-- 雷达感应开关 -->
        <div class="setting-card rounded-2xl p-4 mb-4">
            <div class="flex items-center justify-between">
                <div>
                    <h3 class="text-base font-semibold text-gray-800">雷达感应</h3>
                </div>
                <div class="relative inline-block w-12 h-6">
                    <div class="block w-full h-full bg-blue-500 rounded-full">
                        <span
                            class="absolute left-1 top-1 w-4 h-4 bg-white rounded-full transform translate-x-6"></span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 感应范围设置 -->
        <div class="setting-card rounded-2xl p-4 mb-4">
            <h3 class="text-base font-semibold text-gray-800 mb-3">雷达感应范围(米)</h3>
            <div class="mb-3">
                <div class="grid grid-cols-4 gap-2 mb-4">
                    <!-- 第一行 -->
                    <div class="p-3 bg-gray-100 rounded-lg text-center">
                        <div class="font-medium text-gray-800">0.7</div>
                    </div>
                    <div class="p-3 bg-gray-100 rounded-lg text-center">
                        <div class="font-medium text-gray-800">1.5</div>
                    </div>
                    <div class="p-3 bg-gray-100 rounded-lg text-center">
                        <div class="font-medium text-gray-800">2.3</div>
                    </div>
                    <div class="p-3 rounded-lg text-center active" style="background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);">
                        <div class="font-medium text-white">3</div>
                    </div>
                </div>
                <div class="grid grid-cols-4 gap-2">
                    <!-- 第二行 -->
                    <div class="p-3 bg-gray-100 rounded-lg text-center">
                        <div class="font-medium text-gray-800">3.8</div>
                    </div>
                    <div class="p-3 bg-gray-100 rounded-lg text-center">
                        <div class="font-medium text-gray-800">4.5</div>
                    </div>
                    <div class="p-3 bg-gray-100 rounded-lg text-center">
                        <div class="font-medium text-gray-800">5.3</div>
                    </div>
                    <div class="p-3 bg-gray-100 rounded-lg text-center">
                        <div class="font-medium text-gray-800">6</div>
                    </div>
                </div>
            </div>

        </div>

        <!-- 触发灵敏度 -->
        <div class="setting-card rounded-2xl p-4 mb-4">
            <h3 class="text-base font-semibold text-gray-800 mb-3">触发有人灵敏度</h3>
            <p class="text-sm text-gray-500 mb-3">调节检测到人体存在的敏感程度</p>
            <div class="grid grid-cols-3 gap-3">
                <div class="p-3 bg-gray-50 rounded-xl text-center">
                    <div class="font-medium text-gray-800">低</div>
                </div>
                <div class="p-3 rounded-xl text-center" style="background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);">
                    <div class="font-medium text-white">标准</div>
                </div>
                <div class="p-3 bg-gray-50 rounded-xl text-center">
                    <div class="font-medium text-gray-800">高</div>
                </div>
            </div>
        </div>

        <!-- 保持灵敏度 -->
        <div class="setting-card rounded-2xl p-4 mb-4">
            <h3 class="text-base font-semibold text-gray-800 mb-3">保持有人灵敏度</h3>
            <p class="text-sm text-gray-500 mb-3">调节维持人体存在检测的敏感程度</p>
            <div class="grid grid-cols-3 gap-3">
                <div class="p-3 bg-gray-50 rounded-xl text-center">
                    <div class="font-medium text-gray-800">低</div>
                </div>
                <div class="p-3 rounded-xl text-center" style="background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);">
                    <div class="font-medium text-white">标准</div>
                </div>
                <div class="p-3 bg-gray-50 rounded-xl text-center">
                    <div class="font-medium text-gray-800">高</div>
                </div>
            </div>
        </div>

        <!-- 无人判断时间 -->
        <div class="setting-card rounded-2xl p-4 mb-4">
            <h3 class="text-base font-semibold text-gray-800 mb-3">无人判断时间</h3>
            <p class="text-sm text-gray-500 mb-3">检测不到人体后多长时间判断为无人</p>
            <div class="mb-3">
                <div class="flex items-center justify-between mb-3">
                    <span class="text-sm text-gray-700">延迟时间</span>
                    <span class="text-sm text-blue-600 font-semibold">30秒</span>
                </div>
                <div class="flex items-center space-x-4">
                    <span class="text-sm text-gray-500">5s</span>
                    <div class="flex-1 relative">
                        <div class="h-2 bg-gray-200 rounded-full">
                            <div class="h-full bg-gradient-to-r from-blue-400 to-blue-600 rounded-full" style="width: 10%"></div>
                        </div>
                        <!-- 刻度线 -->
                        <div class="absolute top-3 left-0 right-0 flex justify-between">
                            <div class="w-0.5 h-2 bg-gray-300"></div>
                            <div class="w-0.5 h-2 bg-gray-300"></div>
                            <div class="w-0.5 h-2 bg-gray-300"></div>
                            <div class="w-0.5 h-2 bg-gray-300"></div>
                            <div class="w-0.5 h-2 bg-gray-300"></div>
                        </div>
                    </div>
                    <span class="text-sm text-gray-500">300s</span>
                </div>
            </div>
        </div>

        <!-- 自动感应灯光 -->
        <div class="setting-card rounded-2xl p-4">
            <div class="flex items-center justify-between mb-3">
                <div>
                    <h3 class="text-base font-semibold text-gray-800">自动感应灯光</h3>
                </div>
                <div class="relative inline-block w-12 h-6">
                    <div class="block w-full h-full bg-blue-500 rounded-full">
                        <span
                            class="absolute left-1 top-1 w-4 h-4 bg-white rounded-full transform translate-x-6"></span>
                    </div>
                </div>
            </div>

            <div class="space-y-4">
                <!-- 光照阈值 -->
                <div class="p-4 bg-blue-50 rounded-xl">
                    <div class="flex items-center justify-between mb-3">
                        <span class="text-sm text-gray-700">自动开灯的光照阈值</span>
                        <span class="text-sm text-blue-600 font-semibold">300 Lux 默认</span>
                    </div>
                    <div class="flex items-center space-x-4">
                        <span class="text-sm text-gray-500">0</span>
                        <div class="flex-1 relative">
                            <div class="h-2 bg-gray-200 rounded-full">
                                <div class="h-full bg-gradient-to-r from-blue-400 to-blue-600 rounded-full" style="width: 30%"></div>
                            </div>
                            <!-- 刻度线 -->
                            <div class="absolute top-3 left-0 right-0 flex justify-between">
                                <div class="w-0.5 h-2 bg-gray-300"></div>
                                <div class="w-0.5 h-2 bg-gray-300"></div>
                                <div class="w-0.5 h-2 bg-gray-300"></div>
                                <div class="w-0.5 h-2 bg-gray-300"></div>
                                <div class="w-0.5 h-2 bg-gray-300"></div>
                            </div>
                        </div>
                        <span class="text-sm text-gray-500">1000</span>
                    </div>
                </div>

                <!-- 感应有人设置 -->
                <div class="p-4 bg-green-50 rounded-xl border-2 border-green-200">
                    <div class="flex items-center justify-between mb-3">
                        <div class="flex items-center">
                            <i class="fas fa-user-check text-green-600 mr-2"></i>
                            <span class="text-sm font-medium text-green-700">感应有人时</span>
                        </div>
                        <span class="text-sm text-green-600 font-semibold">85%</span>
                    </div>
                    <div class="mb-2">
                        <div class="text-xs text-green-600 mb-2">灯光亮度设置为：</div>
                        <div class="flex items-center space-x-3">
                            <span class="text-xs text-green-500">1%</span>
                            <div class="flex-1 relative">
                                <div class="h-2 bg-gray-200 rounded-full">
                                    <div class="h-full bg-gradient-to-r from-green-400 to-green-600 rounded-full" style="width: 85%"></div>
                                </div>
                                <!-- 刻度线 -->
                                <div class="absolute top-3 left-0 right-0 flex justify-between">
                                    <div class="w-0.5 h-2 bg-gray-300"></div>
                                    <div class="w-0.5 h-2 bg-gray-300"></div>
                                    <div class="w-0.5 h-2 bg-gray-300"></div>
                                    <div class="w-0.5 h-2 bg-gray-300"></div>
                                    <div class="w-0.5 h-2 bg-gray-300"></div>
                                </div>
                            </div>
                            <span class="text-xs text-green-500">100%</span>
                        </div>
                    </div>

                </div>

                <!-- 感应无人设置 -->
                <div class="p-4 bg-orange-50 rounded-xl border-2 border-orange-200">
                    <div class="flex items-center justify-between mb-3">
                        <div class="flex items-center">
                            <i class="fas fa-user-times text-orange-600 mr-2"></i>
                            <span class="text-sm font-medium text-orange-700">感应无人时</span>
                        </div>
                        <span class="text-sm text-orange-600 font-semibold">0%</span>
                    </div>
                    <div class="mb-2">
                        <div class="text-xs text-orange-600 mb-2">灯光亮度设置为：</div>
                        <div class="flex items-center space-x-3">
                            <span class="text-xs text-orange-500">0%</span>
                            <div class="flex-1 relative">
                                <div class="h-2 bg-gray-200 rounded-full">
                                    <div class="h-full bg-gradient-to-r from-orange-400 to-orange-600 rounded-full" style="width: 0%"></div>
                                </div>
                                <!-- 刻度线 -->
                                <div class="absolute top-3 left-0 right-0 flex justify-between">
                                    <div class="w-0.5 h-2 bg-gray-300"></div>
                                    <div class="w-0.5 h-2 bg-gray-300"></div>
                                    <div class="w-0.5 h-2 bg-gray-300"></div>
                                    <div class="w-0.5 h-2 bg-gray-300"></div>
                                    <div class="w-0.5 h-2 bg-gray-300"></div>
                                </div>
                            </div>
                            <span class="text-xs text-orange-500">100%</span>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>

    <!-- iOS底部指示器 -->
    <div class="fixed bottom-0 left-0 right-0 flex justify-center pt-4 pb-2 bg-transparent">
        <div class="w-32 h-1 bg-black rounded-full"></div>
    </div>
</body>

</html>