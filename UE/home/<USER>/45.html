<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>添加情景模式</title>
    <link href="https://unpkg.com/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            background: linear-gradient(180deg, #e6f3ff 0%, #f0f8ff 50%, #e8f4f8 100%);
        }
        .setting-item {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
        }
        .icon-option {
            transition: all 0.3s ease;
        }
        .icon-option.selected {
            background: #3b82f6;
            color: white;
            transform: scale(1.05);
        }
    </style>
</head>
<body class="h-screen">
    <!-- iOS状态栏 -->
    <div class="fixed top-0 left-0 right-0 text-black text-sm px-4 py-2 flex justify-between items-center z-50"
        style="background: linear-gradient(180deg, #e6f3ff 0%, #f0f8ff 100%);">
        <div class="font-semibold">9:41</div>
        <div class="flex items-center space-x-1">
            <i class="fas fa-signal text-xs"></i>
            <i class="fas fa-wifi text-xs"></i>
            <i class="fas fa-battery-full text-xs"></i>
        </div>
    </div>

    <!-- 顶部导航 -->
    <div class="fixed top-10 left-0 right-0 bg-transparent px-4 py-3 z-40">
        <div class="flex items-center justify-center relative">
            <div class="absolute left-0">
                <i class="fas fa-chevron-left text-gray-600 text-lg"></i>
            </div>
            <h1 class="text-lg font-semibold text-gray-800">添加情景模式</h1>
        </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="pt-24 pb-32">
        <!-- 模式名称 -->
        <div class="mx-4 mb-6">
            <div class="setting-item rounded-xl p-4">
                <h3 class="text-base font-medium text-gray-800 mb-3">模式名称</h3>
                <input type="text" placeholder="请输入模式名称" value="自定义2" 
                    class="w-full p-3 bg-gray-50 rounded-lg border border-gray-200 focus:border-blue-500 focus:outline-none text-gray-800">
            </div>
        </div>



        <!-- 亮度设置 -->
        <div class="mx-4 mb-6">
            <div class="setting-item rounded-xl p-4">
                <h3 class="text-base font-medium text-gray-800 mb-4">亮度设置</h3>
                <div class="flex items-center justify-between mb-3">
                    <span class="text-sm text-gray-700">亮度</span>
                    <span class="text-lg font-semibold text-blue-600">65%</span>
                </div>
                <div class="flex items-center space-x-4">
                    <i class="fas fa-sun text-yellow-500 text-lg"></i>
                    <div class="flex-1 h-3 bg-gray-200 rounded-full">
                        <div class="h-full bg-gradient-to-r from-yellow-400 to-yellow-600 rounded-full" style="width: 65%"></div>
                    </div>
                    <i class="fas fa-sun text-yellow-500 text-xl"></i>
                </div>
                <div class="flex justify-between text-sm text-gray-400 mt-2">
                    <span>1%</span>
                    <span>100%</span>
                </div>
            </div>
        </div>


    </div>

    <!-- 底部按钮 -->
    <div class="fixed bottom-8 left-0 right-0 bg-transparent px-4">
        <div class="flex space-x-3">
            <div class="flex-1 bg-gray-100 text-gray-600 py-4 rounded-xl font-medium text-base text-center">
                取消
            </div>
            <div class="flex-1 bg-blue-500 text-white py-4 rounded-xl font-medium text-base text-center">
                保存
            </div>
        </div>
    </div>

    <!-- iOS底部指示器 -->
    <div class="fixed bottom-0 left-0 right-0 flex justify-center pt-4 pb-2 bg-transparent">
        <div class="w-32 h-1 bg-black rounded-full"></div>
    </div>

</body>
</html>