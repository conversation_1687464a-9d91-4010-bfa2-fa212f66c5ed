<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>定时</title>
    <link href="https://unpkg.com/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            background: linear-gradient(180deg, #e6f3ff 0%, #f0f8ff 50%, #e8f4f8 100%);
        }
        .setting-item {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
        }
    </style>
</head>
<body class="h-screen">
    <!-- iOS状态栏 -->
    <div class="fixed top-0 left-0 right-0 text-black text-sm px-4 py-2 flex justify-between items-center z-50"
        style="background: linear-gradient(180deg, #e6f3ff 0%, #f0f8ff 100%);">
        <div class="font-semibold">9:41</div>
        <div class="flex items-center space-x-1">
            <i class="fas fa-signal text-xs"></i>
            <i class="fas fa-wifi text-xs"></i>
            <i class="fas fa-battery-full text-xs"></i>
        </div>
    </div>

    <!-- 顶部导航 -->
    <div class="fixed top-10 left-0 right-0 bg-transparent px-4 py-3 z-40">
        <div class="flex items-center justify-center relative">
            <div class="absolute left-0">
                <i class="fas fa-chevron-left text-gray-600 text-lg"></i>
            </div>
            <h1 class="text-lg font-semibold text-gray-800">定时</h1>
            <div class="absolute right-0">
                <span class="text-blue-600 font-medium">新增</span>
            </div>
        </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="pt-24 pb-20 px-4">
        <!-- 定时任务列表 -->
        <div class="space-y-4">
            <!-- 定时任务1 - 开启状态 -->
            <div class="setting-item rounded-xl p-4">
                <div class="flex items-center justify-between">
                    <div class="flex-1">
                        <div class="text-2xl font-light text-gray-800 mb-1">13:42</div>
                        <div class="text-sm text-gray-500">开启设备　仅执行一次</div>
                    </div>
                    <div class="relative inline-block w-12 h-6">
                        <div class="block w-full h-full bg-blue-500 rounded-full">
                            <span class="absolute left-1 top-1 w-4 h-4 bg-white rounded-full transform translate-x-6"></span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 定时任务2 - 关闭状态 -->
            <div class="setting-item rounded-xl p-4">
                <div class="flex items-center justify-between">
                    <div class="flex-1">
                        <div class="text-2xl font-light text-gray-800 mb-1">08:42</div>
                        <div class="text-sm text-gray-500">关闭设备　周末</div>
                    </div>
                    <div class="relative inline-block w-12 h-6">
                        <div class="block w-full h-full bg-gray-300 rounded-full">
                            <span class="absolute left-1 top-1 w-4 h-4 bg-white rounded-full"></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- iOS底部指示器 -->
    <div class="fixed bottom-0 left-0 right-0 flex justify-center pt-4 pb-2 bg-transparent">
        <div class="w-32 h-1 bg-black rounded-full"></div>
    </div>

</body>
</html>
