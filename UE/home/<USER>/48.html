<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>新增定时</title>
    <link href="https://unpkg.com/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            background: linear-gradient(180deg, #e6f3ff 0%, #f0f8ff 50%, #e8f4f8 100%);
        }
        .setting-item {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
        }
        .time-picker {
            background: #f8f9fa;
        }
        .time-selected {
            background: rgba(59, 130, 246, 0.1);
            color: #3b82f6;
        }
    </style>
</head>
<body class="h-screen">
    <!-- iOS状态栏 -->
    <div class="fixed top-0 left-0 right-0 text-black text-sm px-4 py-2 flex justify-between items-center z-50"
        style="background: linear-gradient(180deg, #e6f3ff 0%, #f0f8ff 100%);">
        <div class="font-semibold">9:41</div>
        <div class="flex items-center space-x-1">
            <i class="fas fa-signal text-xs"></i>
            <i class="fas fa-wifi text-xs"></i>
            <i class="fas fa-battery-full text-xs"></i>
        </div>
    </div>

    <!-- 顶部导航 -->
    <div class="fixed top-10 left-0 right-0 bg-transparent px-4 py-3 z-40">
        <div class="flex items-center justify-center relative">
            <div class="absolute left-0">
                <i class="fas fa-chevron-left text-gray-600 text-lg"></i>
            </div>
            <h1 class="text-lg font-semibold text-gray-800">新增定时</h1>
            <div class="absolute right-0">
                <span class="text-blue-600 font-medium">保存</span>
            </div>
        </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="pt-24 pb-20 px-4">
        <!-- 定时时间设置 -->
        <div class="setting-item rounded-xl p-4 mb-4">
            <h3 class="text-base font-medium text-gray-800 mb-4">定时时间</h3>
            
            <!-- 时间选择器 -->
            <div class="time-picker rounded-xl p-4">
                <div class="flex justify-center items-center">
                    <!-- 小时选择 -->
                    <div class="flex flex-col items-center">
                        <div class="text-3xl font-light text-gray-400 py-2">21</div>
                        <div class="text-3xl font-light text-gray-400 py-2">22</div>
                        <div class="text-4xl font-light text-gray-800 py-2 time-selected">23</div>
                        <div class="text-3xl font-light text-gray-400 py-2">24</div>
                        <div class="text-3xl font-light text-gray-400 py-2">1</div>
                    </div>
                    
                    <div class="mx-4 text-2xl font-light text-gray-800">时</div>
                    
                    <!-- 分钟选择 -->
                    <div class="flex flex-col items-center">
                        <div class="text-3xl font-light text-gray-400 py-2">5</div>
                        <div class="text-3xl font-light text-gray-400 py-2">6</div>
                        <div class="text-4xl font-light text-gray-800 py-2 time-selected">7</div>
                        <div class="text-3xl font-light text-gray-400 py-2">8</div>
                        <div class="text-3xl font-light text-gray-400 py-2">9</div>
                    </div>
                    
                    <div class="ml-4 text-2xl font-light text-gray-800">分</div>
                </div>
            </div>
        </div>

        <!-- 重复设置 -->
        <div class="setting-item rounded-xl p-4 mb-4">
            <div class="flex items-center justify-between">
                <span class="text-base font-medium text-gray-800">重复</span>
                <div class="flex items-center">
                    <span class="text-gray-500 text-sm mr-2">周一</span>
                    <i class="fas fa-chevron-right text-gray-400 text-sm"></i>
                </div>
            </div>
        </div>

        <!-- 执行动作设置 -->
        <div class="setting-item rounded-xl p-4 mb-4">
            <h3 class="text-base font-medium text-gray-800 mb-4">执行动作</h3>

            <div class="space-y-3">
                <!-- 开启设备 -->
                <div class="p-3 bg-blue-50 rounded-xl border border-blue-200">
                    <span class="text-gray-800 font-medium">开启设备</span>
                </div>

                <!-- 关闭设备 -->
                <div class="p-3 bg-gray-50 rounded-xl border border-gray-200">
                    <span class="text-gray-800 font-medium">关闭设备</span>
                </div>
            </div>
        </div>
    </div>

    <!-- iOS底部指示器 -->
    <div class="fixed bottom-0 left-0 right-0 flex justify-center pt-4 pb-2 bg-transparent">
        <div class="w-32 h-1 bg-black rounded-full"></div>
    </div>

</body>
</html>
