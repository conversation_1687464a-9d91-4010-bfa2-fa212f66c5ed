<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>添加网关</title>
    <link href="https://unpkg.com/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(180deg, #e6f3ff 0%, #f0f8ff 100%);
            overflow: hidden;
        }
        
        .scan-frame {
            position: relative;
            width: 280px;
            height: 280px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
        }
        
        .scan-corners {
            position: absolute;
            width: 100%;
            height: 100%;
        }
        
        .corner {
            position: absolute;
            width: 20px;
            height: 20px;
            border: 3px solid #3B82F6;
        }

        .corner.top-left {
            top: 0;
            left: 0;
            border-right: none;
            border-bottom: none;
        }

        .corner.top-right {
            top: 0;
            right: 0;
            border-left: none;
            border-bottom: none;
        }

        .corner.bottom-left {
            bottom: 0;
            left: 0;
            border-right: none;
            border-top: none;
        }

        .corner.bottom-right {
            bottom: 0;
            right: 0;
            border-left: none;
            border-top: none;
        }
        
        .scan-line {
            position: absolute;
            width: 100%;
            height: 2px;
            background: linear-gradient(90deg, transparent, #3B82F6, transparent);
            top: 50%;
            opacity: 0.6;
        }
    </style>
</head>
<body class="h-screen">
    <div class="flex flex-col h-full">
        <!-- iOS状态栏 -->
        <div class="fixed top-0 left-0 right-0 text-black text-sm px-4 py-2 flex justify-between items-center z-50" style="background: linear-gradient(180deg, #e6f3ff 0%, #f0f8ff 100%);">
            <div class="font-semibold">
                9:41
            </div>
            <div class="flex items-center space-x-1">
                <i class="fas fa-signal text-xs"></i>
                <i class="fas fa-wifi text-xs"></i>
                <i class="fas fa-battery-full text-xs"></i>
            </div>
        </div>

        <!-- 顶部导航栏 -->
        <div class="fixed top-10 left-0 right-0 bg-transparent px-4 py-3 z-40">
            <div class="flex items-center justify-center relative">
                <button class="absolute left-0">
                    <i class="fas fa-chevron-left text-gray-600 text-lg"></i>
                </button>
                <h1 class="text-lg font-semibold text-gray-800">扫码添加</h1>
            </div>
        </div>

        <!-- 主内容区域 -->
        <div class="flex-1 flex flex-col items-center justify-center px-4 pt-20 pb-8">
            <!-- 扫描框 -->
            <div class="scan-frame mb-8">
                <div class="scan-corners">
                    <div class="corner top-left"></div>
                    <div class="corner top-right"></div>
                    <div class="corner bottom-left"></div>
                    <div class="corner bottom-right"></div>
                </div>
                <div class="scan-line"></div>
                
                <!-- 中心图标 -->
                <div class="absolute inset-0 flex items-center justify-center">
                    <div class="relative">
                        <div class="w-16 h-16 bg-blue-500 rounded-full flex items-center justify-center"></div>
                        <div class="absolute inset-0 w-16 h-16 bg-blue-500 rounded-full flex items-center justify-center">
                            <i class="fas fa-qrcode text-white text-2xl"></i>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 提示信息 -->
            <div class="text-center mb-8 max-w-xs">
                <h2 class="text-lg font-semibold text-gray-800 mb-3">扫描设备二维码</h2>
                <p class="text-gray-600 text-sm leading-relaxed">
                    请将摄像头对准5G CPE设备上的二维码进行扫描
                </p>
            </div>


        </div>

        <!-- iOS底部指示器 -->
        <div class="fixed bottom-0 left-0 right-0 flex justify-center pt-4 pb-2 bg-transparent">
            <div class="w-32 h-1 bg-black rounded-full"></div>
        </div>
    </div>
</body>
</html>
