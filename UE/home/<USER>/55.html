<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>更多</title>
    <link href="https://unpkg.com/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            background: linear-gradient(180deg, #e6f3ff 0%, #f0f8ff 50%, #e8f4f8 100%);
        }
        .setting-item {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
        }
    </style>
</head>
<body class="h-screen">
    <!-- iOS状态栏 -->
    <div class="fixed top-0 left-0 right-0 text-black text-sm px-4 py-2 flex justify-between items-center z-50" style="background: linear-gradient(180deg, #e6f3ff 0%, #f0f8ff 100%);">
        <div class="font-semibold">
            9:41
        </div>
        <div class="flex items-center space-x-1">
            <i class="fas fa-signal text-xs"></i>
            <i class="fas fa-wifi text-xs"></i>
            <i class="fas fa-battery-full text-xs"></i>
        </div>
    </div>

    <!-- 顶部导航 -->
    <div class="fixed top-10 left-0 right-0 bg-transparent px-4 py-3 z-40">
        <div class="flex items-center justify-center relative">
            <button class="absolute left-0">
                <i class="fas fa-chevron-left text-gray-600 text-lg"></i>
            </button>
            <h1 class="text-lg font-semibold text-gray-800">更多</h1>
        </div>
    </div>

    <!-- 主内容区域 -->
    <div class="pt-24 pb-32">
        <!-- 功能设置区域 -->
        <div class="mx-4 mt-4">
            <div class="mb-3">
                <h3 class="text-sm font-medium text-gray-500 px-2">功能设置</h3>
            </div>
            
            <div class="space-y-1">
                <!-- 功能设置 -->
                <div class="setting-item rounded-xl p-4 flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                            <i class="fas fa-cogs text-blue-600 text-sm"></i>
                        </div>
                        <span class="text-gray-800 font-medium">通电后状态</span>
                    </div>
                    <div class="flex items-center">
                        <span class="text-gray-500 text-sm mr-2">灯光记忆</span>
                        <i class="fas fa-chevron-right text-gray-400 text-sm"></i>
                    </div>
                </div>

                <!-- 感应设置 -->
                <div class="setting-item rounded-xl p-4 flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center mr-3">
                            <i class="fas fa-sliders-h text-purple-600 text-sm"></i>
                        </div>
                        <span class="text-gray-800 font-medium">感应设置</span>
                    </div>
                    <div class="flex items-center">
                        <i class="fas fa-chevron-right text-gray-400 text-sm"></i>
                    </div>
                </div>

                <!-- 灯光变化 -->
                <div class="setting-item rounded-xl p-4 flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="w-8 h-8 rounded-lg flex items-center justify-center mr-3" style="background-color: #fed7aa;">
                            <i class="fas fa-magic text-orange-600 text-sm"></i>
                        </div>
                        <span class="text-gray-800 font-medium">灯光变化</span>
                    </div>
                    <div class="flex items-center">
                        <span class="text-gray-500 text-sm mr-2">渐变变化</span>
                        <i class="fas fa-chevron-right text-gray-400 text-sm"></i>
                    </div>
                </div>

                <!-- 定时开关灯 -->
                <div class="setting-item rounded-xl p-4 flex items-center justify-between" onclick="window.location.href='light/47.html'">
                    <div class="flex items-center">
                        <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                            <i class="fas fa-clock text-green-600 text-sm"></i>
                        </div>
                        <span class="text-gray-800 font-medium">定时开关灯</span>
                    </div>
                    <div class="flex items-center">
                        <i class="fas fa-chevron-right text-gray-400 text-sm"></i>
                    </div>
                </div>


            </div>
        </div>

        <!-- 通用设置区域 -->
        <div class="mx-4 mt-8">
            <div class="mb-3">
                <h3 class="text-sm font-medium text-gray-500 px-2">通用设置</h3>
            </div>
            
            <div class="space-y-1">
                <!-- 设备名称 -->
                <div class="setting-item rounded-xl p-4 flex items-center justify-between">
                    <span class="text-gray-800 font-medium">设备名称</span>
                    <div class="flex items-center">
                        <span class="text-gray-500 text-sm mr-2">客厅平板灯</span>
                        <i class="fas fa-chevron-right text-gray-400 text-sm"></i>
                    </div>
                </div>

                <!-- 房间位置 -->
                <div class="setting-item rounded-xl p-4 flex items-center justify-between">
                    <span class="text-gray-800 font-medium">房间位置</span>
                    <div class="flex items-center">
                        <span class="text-gray-500 text-sm mr-2">客厅</span>
                        <i class="fas fa-chevron-right text-gray-400 text-sm"></i>
                    </div>
                </div>

                <!-- 设备分享 -->
                <div class="setting-item rounded-xl p-4 flex items-center justify-between">
                    <span class="text-gray-800 font-medium">设备分享</span>
                    <div class="flex items-center">
                        <i class="fas fa-chevron-right text-gray-400 text-sm"></i>
                    </div>
                </div>

                <!-- 设备信息 -->
                <div class="setting-item rounded-xl p-4 flex items-center justify-between">
                    <span class="text-gray-800 font-medium">设备信息</span>
                    <div class="flex items-center">
                        <i class="fas fa-chevron-right text-gray-400 text-sm"></i>
                    </div>
                </div>

                <!-- 设备日志 -->
                <div class="setting-item rounded-xl p-4 flex items-center justify-between">
                    <span class="text-gray-800 font-medium">设备日志</span>
                    <div class="flex items-center">
                        <i class="fas fa-chevron-right text-gray-400 text-sm"></i>
                    </div>
                </div>

                <!-- 固件升级 -->
                <div class="setting-item rounded-xl p-4 flex items-center justify-between">
                    <span class="text-gray-800 font-medium">固件升级</span>
                    <div class="flex items-center">
                        <span class="text-gray-500 text-sm mr-2">v1.2.3</span>
                        <i class="fas fa-chevron-right text-gray-400 text-sm"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 底部删除按钮 -->
    <div class="fixed bottom-8 left-0 right-0 px-4">
        <button class="w-full bg-red-500 text-white py-4 rounded-xl font-medium text-base">
            删除设备
        </button>
    </div>

    <!-- iOS底部指示器 -->
    <div class="fixed bottom-0 left-0 right-0 flex justify-center pt-4 pb-2 bg-transparent">
        <div class="w-32 h-1 bg-black rounded-full"></div>
    </div>

</body>
</html>