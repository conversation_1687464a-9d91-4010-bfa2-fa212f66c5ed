<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>对话记录</title>
    <link href="https://unpkg.com/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            background: linear-gradient(180deg, #e6f3ff 0%, #f0f8ff 50%, #e8f4f8 100%);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        .user-message {
            background: #1890ff;
            color: white;
        }
        .assistant-message {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        .timestamp {
            background: #d9d9d9;
            color: #666;
        }
    </style>
</head>
<body class="h-screen">
    <!-- iOS状态栏 -->
    <div class="fixed top-0 left-0 right-0 text-black text-sm px-4 py-2 flex justify-between items-center z-50"
        style="background: linear-gradient(180deg, #e6f3ff 0%, #f0f8ff 100%);">
        <div class="font-semibold">
            9:41
        </div>
        <div class="flex items-center space-x-1">
            <i class="fas fa-signal text-xs"></i>
            <i class="fas fa-wifi text-xs"></i>
            <i class="fas fa-battery-full text-xs"></i>
        </div>
    </div>

    <!-- 顶部导航 -->
    <div class="fixed top-10 left-0 right-0 bg-transparent px-4 py-3 z-40">
        <div class="flex items-center justify-center relative">
            <button class="absolute left-0">
                <i class="fas fa-chevron-left text-gray-600 text-lg"></i>
            </button>
            <h1 class="text-lg font-semibold text-gray-800">对话记录</h1>
            <button class="absolute right-0 text-red-500 text-sm font-medium">
                清除记录
            </button>
        </div>
    </div>

    <!-- 主内容区域 -->
    <div class="flex-1 overflow-y-auto pt-24 pb-20">
        <!-- 日期筛选 -->
        <div class="mx-4 mb-4">
            <div class="flex space-x-2">
                <button class="bg-blue-500 text-white px-4 py-2 rounded-lg text-sm font-medium">今天</button>
                <button class="bg-gray-100 text-gray-600 px-4 py-2 rounded-lg text-sm">昨天</button>
                <button class="bg-gray-100 text-gray-600 px-4 py-2 rounded-lg text-sm">本周</button>
                <button class="bg-gray-100 text-gray-600 px-4 py-2 rounded-lg text-sm">本月</button>
            </div>
        </div>

        <div class="mx-4">

        <!-- 对话记录列表 -->
        <div class="space-y-4">
            <!-- 时间戳 -->
            <div class="flex justify-center mb-4">
                <span class="timestamp px-3 py-1 rounded-full text-xs">03-03 09:35</span>
            </div>

            <!-- 对话组 1 -->
            <div class="space-y-3">
                <!-- 用户消息 -->
                <div class="flex items-start justify-end space-x-2">
                    <button class="text-gray-400 text-xs mt-2">报错</button>
                    <div class="user-message max-w-xs rounded-2xl px-4 py-3">
                        <p>小觅，播放一些轻音乐</p>
                    </div>
                </div>

                <!-- 助手回复 -->
                <div class="flex justify-start">
                    <div class="assistant-message max-w-xs rounded-2xl px-4 py-3">
                        <p class="text-gray-800 text-sm leading-relaxed">好的，正在为您播放轻音乐</p>
                    </div>
                </div>
            </div>

            <!-- 时间戳 -->
            <div class="flex justify-center mb-4 mt-6">
                <span class="timestamp px-3 py-1 rounded-full text-xs">03-03 09:35</span>
            </div>

            <!-- 对话组 2 -->
            <div class="space-y-3">
                <!-- 用户消息 -->
                <div class="flex items-start justify-end space-x-2">
                    <button class="text-gray-400 text-xs mt-2">报错</button>
                    <div class="user-message max-w-xs rounded-2xl px-4 py-3">
                        <p>今天天气怎么样？</p>
                    </div>
                </div>

                <!-- 助手回复 -->
                <div class="flex justify-start">
                    <div class="assistant-message max-w-xs rounded-2xl px-4 py-3">
                        <p class="text-gray-800 text-sm leading-relaxed">今天多云，气温22-28℃，适合外出</p>
                    </div>
                </div>
            </div>

            <!-- 时间戳 -->
            <div class="flex justify-center mb-4 mt-6">
                <span class="timestamp px-3 py-1 rounded-full text-xs">03-03 09:37</span>
            </div>

            <!-- 对话组 3 -->
            <div class="space-y-3">
                <!-- 用户消息 -->
                <div class="flex items-start justify-end space-x-2">
                    <button class="text-gray-400 text-xs mt-2">报错</button>
                    <div class="user-message max-w-xs rounded-2xl px-4 py-3">
                        <p>打开客厅的灯</p>
                    </div>
                </div>

                <!-- 助手回复 -->
                <div class="flex justify-start">
                    <div class="assistant-message max-w-xs rounded-2xl px-4 py-3">
                        <p class="text-gray-800 text-sm leading-relaxed">客厅灯光已打开</p>
                    </div>
                </div>
            </div>
        </div>
        </div>
    </div>

    <!-- iOS底部指示器 -->
    <div class="fixed bottom-0 left-0 right-0 flex justify-center pt-4 pb-2 bg-transparent">
        <div class="w-32 h-1 bg-black rounded-full"></div>
    </div>
</body>
</html>
