<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>开合帘</title>
    <link href="https://unpkg.com/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            background: linear-gradient(180deg, #e6f3ff 0%, #f0f8ff 50%, #e8f4f8 100%);
        }
        .setting-item {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
        }
        .curtain-visual {
            background: linear-gradient(to bottom,
                rgba(200, 200, 200, 0.8) 0%,
                rgba(220, 220, 220, 0.9) 50%,
                rgba(200, 200, 200, 0.8) 100%);
            background-image: repeating-linear-gradient(
                90deg,
                transparent,
                transparent 2px,
                rgba(0, 0, 0, 0.1) 2px,
                rgba(0, 0, 0, 0.1) 4px
            );
        }
    </style>
</head>
<body class="h-screen">
    <!-- iOS状态栏 -->
    <div class="fixed top-0 left-0 right-0 text-black text-sm px-4 py-2 flex justify-between items-center z-50" style="background: linear-gradient(180deg, #e6f3ff 0%, #f0f8ff 100%);">
        <div class="font-semibold">
            9:41
        </div>
        <div class="flex items-center space-x-1">
            <i class="fas fa-signal text-xs"></i>
            <i class="fas fa-wifi text-xs"></i>
            <i class="fas fa-battery-full text-xs"></i>
        </div>
    </div>

    <!-- 顶部导航 -->
    <div class="fixed top-10 left-0 right-0 bg-transparent px-4 py-3 z-40">
        <div class="flex items-center justify-center relative">
            <div class="absolute left-0">
                <i class="fas fa-chevron-left text-gray-600 text-lg"></i>
            </div>
            <h1 class="text-lg font-semibold text-gray-800">开合帘</h1>
            <div class="absolute right-0">
                <i class="fas fa-ellipsis-h text-gray-600 text-lg"></i>
            </div>
        </div>
    </div>

    <!-- 主内容区域 -->
    <div class="pt-24 pb-8">
        <!-- 设备控制区域 -->
        <div class="mx-4 mb-3">
            <div class="setting-item rounded-xl p-3">
                <!-- 设备状态 -->
                <div class="flex items-center">
                    <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                        <i class="fas fa-window-restore text-blue-600 text-sm"></i>
                    </div>
                    <div>
                        <h2 class="text-sm font-semibold text-gray-800">卧室开合帘</h2>
                        <div class="flex items-center mt-0.5">
                            <div class="w-1.5 h-1.5 bg-green-500 rounded-full mr-1.5"></div>
                            <span class="text-xs text-gray-500">在线</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 窗帘可视化区域 -->
        <div class="mx-4 mb-3">
            <div class="setting-item rounded-xl p-4">
                <!-- 窗帘视觉效果 -->
                <div class="curtain-visual w-full h-40 rounded-xl relative overflow-hidden mb-3">
                    <!-- 窗帘纹理 -->
                    <div class="absolute inset-0 opacity-60">
                        <div class="w-full h-full curtain-visual"></div>
                    </div>
                    
                    <!-- 控制指示器 -->
                    <div class="absolute inset-0 flex items-center justify-center">
                        <div class="flex items-center space-x-3">
                            <button class="w-6 h-6 bg-black bg-opacity-20 rounded-full flex items-center justify-center">
                                <i class="fas fa-chevron-left text-white text-xs"></i>
                            </button>
                            <button class="w-6 h-6 bg-black bg-opacity-20 rounded-full flex items-center justify-center">
                                <i class="fas fa-chevron-right text-white text-xs"></i>
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- 开合状态显示 -->
                <div class="text-center mb-3">
                    <p class="text-gray-600 text-sm">开合: <span class="font-semibold text-gray-800">0%</span></p>
                </div>

                <!-- 百分比快捷键 -->
                <div class="flex justify-between px-4">
                    <button class="px-4 py-1 bg-gray-100 text-gray-700 rounded-lg text-xs font-medium">
                        25%
                    </button>
                    <button class="px-4 py-1 bg-gray-100 text-gray-700 rounded-lg text-xs font-medium">
                        50%
                    </button>
                    <button class="px-4 py-1 bg-gray-100 text-gray-700 rounded-lg text-xs font-medium">
                        75%
                    </button>
                </div>
            </div>
        </div>

        <!-- 底部控制按钮 -->
        <div class="mx-4">
            <div class="setting-item rounded-xl p-4">
                <div class="grid grid-cols-3 gap-3">
                    <!-- 全关按钮 -->
                    <button class="flex flex-col items-center py-2">
                        <div class="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center mb-1">
                            <i class="fas fa-window-minimize text-gray-600 text-sm"></i>
                        </div>
                        <span class="text-xs text-gray-700 font-medium">全关</span>
                    </button>

                    <!-- 暂停按钮 -->
                    <button class="flex flex-col items-center py-2">
                        <div class="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center mb-1">
                            <i class="fas fa-pause text-gray-600 text-sm"></i>
                        </div>
                        <span class="text-xs text-gray-700 font-medium">暂停</span>
                    </button>

                    <!-- 全开按钮 -->
                    <button class="flex flex-col items-center py-2">
                        <div class="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center mb-1">
                            <i class="fas fa-window-maximize text-gray-600 text-sm"></i>
                        </div>
                        <span class="text-xs text-gray-700 font-medium">全开</span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- iOS底部指示器 -->
    <div class="fixed bottom-0 left-0 right-0 flex justify-center pt-4 pb-2 bg-transparent">
        <div class="w-32 h-1 bg-black rounded-full"></div>
    </div>
</body>
</html>
