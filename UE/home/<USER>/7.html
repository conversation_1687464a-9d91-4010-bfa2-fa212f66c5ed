<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设备管理</title>
    <link href="https://unpkg.com/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            background: linear-gradient(180deg, #e6f3ff 0%, #f0f8ff 50%, #e8f4f8 100%);
        }

        .device-card {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>

<body class="min-h-screen">
    <!-- iOS状态栏 -->
    <div class="fixed top-0 left-0 right-0 text-black text-sm px-4 py-2 flex justify-between items-center z-50"
        style="background: linear-gradient(180deg, #e6f3ff 0%, #f0f8ff 100%);">
        <div class="font-semibold">
            9:41
        </div>
        <div class="flex items-center space-x-1">
            <i class="fas fa-signal text-xs"></i>
            <i class="fas fa-wifi text-xs"></i>
            <i class="fas fa-battery-full text-xs"></i>
        </div>
    </div>

    <!-- 顶部导航 -->
    <div class="fixed top-10 left-0 right-0 bg-transparent px-4 py-3 z-40">
        <div class="flex items-center justify-center relative">
            <button class="absolute left-0">
                <i class="fas fa-chevron-left text-gray-600 text-lg"></i>
            </button>
            <h1 class="text-lg font-semibold text-gray-800">连接管理</h1>
        </div>
    </div>

    <!-- 主内容区域 -->
    <div class="pt-24 pb-8">
        <!-- 标签切换 -->
        <div class="mx-4 mt-4">
            <div class="device-card rounded-2xl p-4">
                <div class="flex bg-gray-100 rounded-xl p-1">
                    <button class="flex-1 py-2 px-4 bg-white text-blue-600 font-medium rounded-lg shadow-sm">
                        已连接 (8)
                    </button>
                    <button class="flex-1 py-2 px-4 text-gray-600 font-medium rounded-lg">
                        黑名单 (2)
                    </button>
                </div>
            </div>
        </div>
        <!-- 连接统计 -->
        <div class="device-card mx-4 mt-4 rounded-2xl p-4">
            <div class="grid grid-cols-3 gap-4 text-center">
                <div>
                    <p class="text-2xl font-bold text-blue-600">8</p>
                    <p class="text-sm text-gray-600">已连接设备</p>
                </div>
                <div>
                    <p class="text-2xl font-bold text-green-600">5</p>
                    <p class="text-sm text-gray-600">2.4G设备</p>
                </div>
                <div>
                    <p class="text-2xl font-bold text-purple-600">3</p>
                    <p class="text-sm text-gray-600">5G设备</p>
                </div>
            </div>
        </div>

        <!-- 设备列表 -->
        <div class="mx-4 mt-4">
            <div class="flex items-center justify-between mb-3">
                <h3 class="text-lg font-semibold text-gray-800">已连接设备</h3>
                <button class="text-blue-500 text-sm">批量管理</button>
            </div>

            <!-- 设备项 -->
            <div class="space-y-3">
                <!-- iPhone -->
                <div class="device-card rounded-2xl p-4">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-12 h-12 bg-gray-800 rounded-xl flex items-center justify-center mr-3">
                                <i class="fas fa-mobile-alt text-white text-xl"></i>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-800">iPhone 13 Pro</h4>
                                <p class="text-sm text-gray-500">5G网络 · *************</p>
                                <p class="text-xs text-green-600">在线 · 今日已用 1.2GB</p>
                            </div>
                        </div>
                        <button class="text-blue-500 p-2 rounded-lg">
                            <i class="fas fa-ellipsis-v"></i>
                        </button>
                    </div>
                    <div class="mt-3 flex space-x-2">
                        <button class="bg-blue-50 text-blue-600 px-3 py-1 rounded-lg text-sm">允许上网</button>
                        <button class="bg-gray-100 text-gray-600 px-3 py-1 rounded-lg text-sm">时间设置</button>
                        <button class="bg-red-50 text-red-600 px-3 py-1 rounded-lg text-sm">加入黑名单</button>
                    </div>
                </div>

                <!-- MacBook -->
                <div class="device-card rounded-2xl p-4">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-12 h-12 bg-gray-600 rounded-xl flex items-center justify-center mr-3">
                                <i class="fas fa-laptop text-white text-xl"></i>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-800">MacBook Pro</h4>
                                <p class="text-sm text-gray-500">5G网络 · *************</p>
                                <p class="text-xs text-green-600">在线 · 今日已用 2.8GB</p>
                            </div>
                        </div>
                        <button class="text-blue-500">
                            <i class="fas fa-ellipsis-v"></i>
                        </button>
                    </div>
                    <div class="mt-3 flex space-x-2">
                        <button class="bg-blue-50 text-blue-600 px-3 py-1 rounded-lg text-sm">允许上网</button>
                        <button class="bg-gray-100 text-gray-600 px-3 py-1 rounded-lg text-sm">时间设置</button>
                        <button class="bg-red-50 text-red-600 px-3 py-1 rounded-lg text-sm">加入黑名单</button>
                    </div>
                </div>

                <!-- iPad -->
                <div class="device-card rounded-2xl p-4">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-12 h-12 bg-blue-500 rounded-xl flex items-center justify-center mr-3">
                                <i class="fas fa-tablet-alt text-white text-xl"></i>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-800">iPad Air</h4>
                                <p class="text-sm text-gray-500">2.4G网络 · *************</p>
                                <p class="text-xs text-gray-400">离线 · 最后在线 2小时前</p>
                            </div>
                        </div>
                        <button class="text-blue-500">
                            <i class="fas fa-ellipsis-v"></i>
                        </button>
                    </div>
                    <div class="mt-3 flex space-x-2">
                        <button class="bg-gray-100 text-gray-600 px-3 py-1 rounded-lg text-sm">禁止上网</button>
                        <button class="bg-gray-100 text-gray-600 px-3 py-1 rounded-lg text-sm">时间设置</button>
                        <button class="bg-red-50 text-red-600 px-3 py-1 rounded-lg text-sm">加入黑名单</button>
                    </div>
                </div>

                <!-- 智能电视 -->
                <div class="device-card rounded-2xl p-4">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-12 h-12 bg-purple-500 rounded-xl flex items-center justify-center mr-3">
                                <i class="fas fa-tv text-white text-xl"></i>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-800">小米电视</h4>
                                <p class="text-sm text-gray-500">2.4G网络 · *************</p>
                                <p class="text-xs text-green-600">在线 · 今日已用 3.5GB</p>
                            </div>
                        </div>
                        <button class="text-blue-500 p-2 rounded-lg">
                            <i class="fas fa-ellipsis-v"></i>
                        </button>
                    </div>
                    <div class="mt-3 flex space-x-2">
                        <button class="bg-blue-50 text-blue-600 px-3 py-1 rounded-lg text-sm">允许上网</button>
                        <button class="bg-orange-50 text-orange-600 px-3 py-1 rounded-lg text-sm">限时上网</button>
                        <button class="bg-red-50 text-red-600 px-3 py-1 rounded-lg text-sm">加入黑名单</button>
                    </div>
                </div>

                <!-- 智能音箱 -->
                <div class="device-card rounded-2xl p-4">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-12 h-12 bg-green-500 rounded-xl flex items-center justify-center mr-3">
                                <i class="fas fa-volume-up text-white text-xl"></i>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-800">小爱音箱</h4>
                                <p class="text-sm text-gray-500">2.4G网络 · *************</p>
                                <p class="text-xs text-green-600">在线 · 今日已用 0.1GB</p>
                            </div>
                        </div>
                        <button class="text-blue-500">
                            <i class="fas fa-ellipsis-v"></i>
                        </button>
                    </div>
                    <div class="mt-3 flex space-x-2">
                        <button class="bg-blue-50 text-blue-600 px-3 py-1 rounded-lg text-sm">允许上网</button>
                        <button class="bg-gray-100 text-gray-600 px-3 py-1 rounded-lg text-sm">时间设置</button>
                        <button class="bg-red-50 text-red-600 px-3 py-1 rounded-lg text-sm">加入黑名单</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- iOS底部指示器 -->
    <div class="fixed bottom-0 left-0 right-0 flex justify-center pt-4 pb-2 bg-transparent">
        <div class="w-32 h-1 bg-black rounded-full"></div>
    </div>
</body>
</html>