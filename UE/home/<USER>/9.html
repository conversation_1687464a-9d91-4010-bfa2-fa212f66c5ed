<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>发现设备</title>
    <link href="https://unpkg.com/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            background: linear-gradient(180deg, #e6f3ff 0%, #f0f8ff 50%, #e8f4f8 100%);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        .device-card {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        .empty-state {
            background: transparent;
        }

        /* 搜索状态样式 */
        .search-status {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(59, 130, 246, 0.2);
            box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);
        }

        /* 时间圆环样式 */
        .time-circle {
            position: relative;
            width: 60px;
            height: 60px;
            border: 3px solid #e5e7eb;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            background: white;
        }

        .time-circle::before {
            content: '';
            position: absolute;
            top: -3px;
            left: -3px;
            width: 60px;
            height: 60px;
            border: 3px solid transparent;
            border-top-color: #3b82f6;
            border-radius: 50%;
            transform: rotate(270deg);
        }

    </style>
</head>
<body class="h-screen">
    <!-- iOS状态栏 -->
    <div class="fixed top-0 left-0 right-0 text-black text-sm px-4 py-2 flex justify-between items-center z-50"
        style="background: linear-gradient(180deg, #e6f3ff 0%, #f0f8ff 100%);">
        <div class="font-semibold">
            9:41
        </div>
        <div class="flex items-center space-x-1">
            <i class="fas fa-signal text-xs"></i>
            <i class="fas fa-wifi text-xs"></i>
            <i class="fas fa-battery-full text-xs"></i>
        </div>
    </div>

    <!-- 顶部导航 -->
    <div class="fixed top-10 left-0 right-0 bg-transparent px-4 py-3 z-40">
        <div class="flex items-center justify-center relative">
            <button class="absolute left-0">
                <i class="fas fa-chevron-left text-gray-600 text-lg"></i>
            </button>
            <h1 class="text-lg font-semibold text-gray-800">添加设备</h1>
        </div>
    </div>

    <!-- 主内容区域 -->
    <div class="pt-24 pb-20 px-4">
        <!-- 搜索状态指示器 -->
        <div class="search-status rounded-xl p-4 mb-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                        <i class="fas fa-search text-blue-600"></i>
                    </div>
                    <div>
                        <div class="font-semibold text-gray-800">搜索超时</div>
                        <div class="text-sm text-gray-500">未发现设备</div>
                    </div>
                </div>
                <div class="time-circle">
                    <div class="text-center">
                        <div class="text-lg font-bold text-blue-600">
                            <i class="fas fa-sync-alt"></i>
                        </div>
                        <div class="text-xs text-gray-500">刷新</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 搜索提示 -->
        <div class="mb-6 px-2">
            <p class="text-gray-600 text-sm text-center">
                点击刷新重新搜索，或尝试按型号添加
            </p>
        </div>



        <!-- 空状态 - 占用与设备列表相同的空间 -->
        <div class="mb-4">
            <!-- 占位标题区域 -->
        </div>
        <div class="empty-state text-center mb-8" style="height: 280px; display: flex; flex-direction: column; justify-content: center;">
            <div class="mb-6">
                <!-- 空状态插画 -->
                <div class="mb-6 flex justify-center">
                    <svg width="120" height="120" viewBox="0 0 120 120" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <!-- 背景圆 -->
                        <circle cx="60" cy="60" r="60" fill="#F8F9FA"/>

                        <!-- 搜索框 -->
                        <circle cx="50" cy="50" r="15" fill="none" stroke="#D1D5DB" stroke-width="2"/>
                        <line x1="61" y1="61" x2="70" y2="70" stroke="#D1D5DB" stroke-width="2" stroke-linecap="round"/>

                        <!-- 设备轮廓（虚线表示未找到） -->
                        <rect x="35" y="75" width="20" height="15" rx="2" fill="none" stroke="#E5E7EB" stroke-width="1.5" stroke-dasharray="3,3"/>
                        <rect x="65" y="75" width="20" height="15" rx="2" fill="none" stroke="#E5E7EB" stroke-width="1.5" stroke-dasharray="3,3"/>

                        <!-- 信号波纹（断开状态） -->
                        <path d="M45 35 Q50 30 55 35" stroke="#E5E7EB" stroke-width="1.5" fill="none" stroke-linecap="round" stroke-dasharray="2,2"/>
                        <path d="M65 35 Q70 30 75 35" stroke="#E5E7EB" stroke-width="1.5" fill="none" stroke-linecap="round" stroke-dasharray="2,2"/>

                        <!-- 感叹号 -->
                        <circle cx="60" cy="100" r="6" fill="#9CA3AF"/>
                        <line x1="60" y1="96" x2="60" y2="100" stroke="white" stroke-width="1.5" stroke-linecap="round"/>
                        <circle cx="60" cy="103" r="0.8" fill="white"/>
                    </svg>
                </div>
                <h3 class="text-base font-medium text-gray-600 mb-2">暂无设备</h3>
            </div>
        </div>



        <!-- 按型号添加入口 -->
        <div class="text-center py-6">
            <p class="text-gray-500 text-sm mb-2 text-center">设备无法自动发现？</p>
            <div class="flex justify-center">
                <a href="#" class="text-blue-600 font-medium text-sm inline-flex items-center">
                    <i class="fas fa-plus text-blue-600 text-xs mr-1"></i>
                    按型号添加
                </a>
            </div>
        </div>
    </div>

    <!-- 底部按钮 -->
    <div class="fixed bottom-8 left-0 right-0 bg-transparent px-4">
        <button class="w-full bg-gray-300 text-gray-500 py-4 rounded-xl font-medium text-base cursor-not-allowed" disabled>
            一键绑定
        </button>
    </div>

    <!-- iOS底部指示器 -->
    <div class="fixed bottom-0 left-0 right-0 flex justify-center pt-4 pb-2 bg-transparent">
        <div class="w-32 h-1 bg-black rounded-full"></div>
    </div>
</body>
</html>
