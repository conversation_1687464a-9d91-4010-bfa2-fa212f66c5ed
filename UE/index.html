<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>合觅App原型UE设计 - 页面总览</title>
    <link href="https://unpkg.com/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            background-color: #f6f6f6;
        }
        .iframe-container {
            background-color: #f6f6f6;
        }
        iframe {
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            max-width: 100%;
        }

        /* 响应式iframe尺寸 */
        @media (min-width: 1024px) {
            .responsive-iframe {
                width: 393px;
                height: 852px;
            }
        }

        @media (max-width: 1023px) and (min-width: 768px) {
            .responsive-iframe {
                width: 280px;
                height: 607px;
            }
        }

        @media (max-width: 767px) {
            .responsive-iframe {
                width: 100%;
                max-width: 393px;
                height: 852px;
            }
        }
    </style>
</head>
<body class="min-h-screen p-2 md:p-6 lg:p-8">
    <div class="max-w-7xl mx-auto">
        <!-- 页面标题 -->
        <div class="text-center mb-8">
            <h1 class="text-2xl md:text-3xl lg:text-4xl font-bold text-gray-800 mb-2">合觅App原型UE设计</h1>
        </div>

        <!-- 主导航页 -->
        <div class="mb-8">
            <h2 class="text-xl md:text-2xl font-bold text-gray-800 mb-4 md:mb-6 text-center">主导航页</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2 md:gap-6 lg:gap-8 iframe-container mb-6">
                <!-- 首页 -->
                <div class="text-center">
                    <div class="flex flex-col sm:flex-row items-center justify-center mb-1">
                        <h3 class="text-lg md:text-xl font-semibold text-gray-700 mb-1 sm:mb-0 sm:mr-3">首页</h3>
                        <button onclick="window.location.href='home/sub-index.html'" class="text-blue-600 hover:text-blue-800 text-xs md:text-sm">
                            <i class="fas fa-external-link-alt mr-1"></i>查看更多
                        </button>
                    </div>
                    <iframe src="home/01.html" class="responsive-iframe mx-auto mb-2"></iframe>
                    <p class="text-sm text-gray-600"><span class="text-blue-600 cursor-pointer hover:text-blue-800 hover:underline" onclick="window.location.href='home/01.html'">01</span></p>
                </div>

                <!-- 场景 -->
                <div class="text-center">
                    <div class="flex items-center justify-center mb-1">
                        <h3 class="text-xl font-semibold text-gray-700 mr-3">场景</h3>
                        <button onclick="window.location.href='scenes/sub-index.html'" class="text-blue-600 hover:text-blue-800 text-sm">
                            <i class="fas fa-external-link-alt mr-1"></i>查看更多
                        </button>
                    </div>
                    <iframe src="scenes/02.html" class="responsive-iframe mx-auto mb-2"></iframe>
                    <p class="text-sm text-gray-600"><span class="text-blue-600 cursor-pointer hover:text-blue-800 hover:underline" onclick="window.location.href='scenes/02.html'">02</span></p>
                </div>

                <!-- AI智能体 -->
                <div class="text-center">
                    <div class="flex items-center justify-center mb-1">
                        <h3 class="text-xl font-semibold text-gray-700 mr-3">AI智能体</h3>
                        <button onclick="window.location.href='ai-assistant/sub-index.html'" class="text-blue-600 hover:text-blue-800 text-sm">
                            <i class="fas fa-external-link-alt mr-1"></i>查看更多
                        </button>
                    </div>
                    <iframe src="ai-assistant/03.html" class="responsive-iframe mx-auto mb-2"></iframe>
                    <p class="text-sm text-gray-600"><span class="text-blue-600 cursor-pointer hover:text-blue-800 hover:underline" onclick="window.location.href='ai-assistant/03.html'">03</span></p>
                </div>
            </div>

            <!-- 第二行一级页面 -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2 md:gap-6 lg:gap-8 iframe-container">
                <!-- 商城 -->
                <div class="text-center">
                    <div class="flex flex-col sm:flex-row items-center justify-center mb-1">
                        <h3 class="text-lg md:text-xl font-semibold text-gray-700 mb-1 sm:mb-0 sm:mr-3">商城</h3>
                        <button onclick="window.location.href='mall/sub-index.html'" class="text-blue-600 hover:text-blue-800 text-xs md:text-sm">
                            <i class="fas fa-external-link-alt mr-1"></i>查看更多
                        </button>
                    </div>
                    <iframe src="mall/04.html" class="responsive-iframe mx-auto mb-2"></iframe>
                    <p class="text-sm text-gray-600"><span class="text-blue-600 cursor-pointer hover:text-blue-800 hover:underline" onclick="window.location.href='mall/04.html'">04</span></p>
                </div>

                <!-- 我的 -->
                <div class="text-center">
                    <div class="flex flex-col sm:flex-row items-center justify-center mb-1">
                        <h3 class="text-lg md:text-xl font-semibold text-gray-700 mb-1 sm:mb-0 sm:mr-3">我的</h3>
                        <button onclick="window.location.href='profile/sub-index.html'" class="text-blue-600 hover:text-blue-800 text-xs md:text-sm">
                            <i class="fas fa-external-link-alt mr-1"></i>查看更多
                        </button>
                    </div>
                    <iframe src="profile/05.html" class="responsive-iframe mx-auto mb-2"></iframe>
                    <p class="text-sm text-gray-600"><span class="text-blue-600 cursor-pointer hover:text-blue-800 hover:underline" onclick="window.location.href='profile/05.html'">05</span></p>
                </div>
            </div>
        </div>



    </div>
</body>
</html>
