<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商城</title>
    <link href="https://unpkg.com/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            background: linear-gradient(180deg, #e6f3ff 0%, #f0f8ff 50%, #e8f4f8 100%);
        }
        .device-card {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body class="min-h-screen flex flex-col">
    <!-- iOS状态栏 -->
    <div class="fixed top-0 left-0 right-0 text-black text-sm px-4 py-2 flex justify-between items-center z-50" style="background: linear-gradient(180deg, #e6f3ff 0%, #f0f8ff 100%);">
        <div class="font-semibold">
            9:41
        </div>
        <div class="flex items-center space-x-1">
            <i class="fas fa-signal text-xs"></i>
            <i class="fas fa-wifi text-xs"></i>
            <i class="fas fa-battery-full text-xs"></i>
        </div>
    </div>

    <!-- 主内容区域 -->
    <div class="flex-1 overflow-y-auto pt-12 pb-20">
        <!-- 顶部导航 -->
        <div class="flex justify-between items-center p-4">
            <div>
                <h1 class="text-2xl font-bold text-gray-800">商城</h1>
                <p class="text-sm text-gray-600">智能设备，一站购齐</p>
            </div>
            <div class="flex space-x-4">
                <button class="p-3 bg-white bg-opacity-60 rounded-full">
                    <i class="fas fa-search text-gray-600"></i>
                </button>
                <button class="p-3 bg-white bg-opacity-60 rounded-full">
                    <i class="fas fa-shopping-cart text-gray-600"></i>
                </button>
            </div>
        </div>
        <!-- 已购商品 -->
        <div class="px-4 mb-4">
            <div class="device-card rounded-2xl p-4">
                <div class="mb-4">
                    <h2 class="text-lg font-bold text-gray-800">已购商品</h2>
                </div>

                <!-- 筛选标签 -->
                <div class="flex space-x-2 mb-4 overflow-x-auto">
                    <button class="bg-blue-500 text-white px-3 py-2 rounded-lg text-xs font-medium whitespace-nowrap">
                        全部 (3)
                    </button>
                    <button class="bg-gray-100 text-gray-600 px-3 py-2 rounded-lg text-xs font-medium whitespace-nowrap">
                        使用中 (2)
                    </button>
                    <button class="bg-gray-100 text-gray-600 px-3 py-2 rounded-lg text-xs font-medium whitespace-nowrap">
                        待续费 (1)
                    </button>
                    <button class="bg-gray-100 text-gray-600 px-3 py-2 rounded-lg text-xs font-medium whitespace-nowrap">
                        已过期 (0)
                    </button>
                </div>

                <!-- 数字商品列表 -->
                <div class="space-y-3">
                    <!-- 5G畅享套餐 -->
                    <div class="border border-green-200 rounded-xl p-3 bg-green-50">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center mr-3">
                                    <i class="fas fa-sim-card text-white"></i>
                                </div>
                                <div>
                                    <h4 class="font-semibold text-gray-800 text-sm">5G畅享套餐</h4>
                                    <p class="text-xs text-green-600">使用中 · 剩余23天</p>
                                </div>
                            </div>
                            <button class="bg-green-500 text-white px-3 py-1 rounded-lg text-xs">续费</button>
                        </div>
                    </div>

                    <!-- 喜马拉雅VIP -->
                    <div class="border border-purple-200 rounded-xl p-3 bg-purple-50">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-purple-600 rounded-lg flex items-center justify-center mr-3">
                                    <i class="fas fa-headphones text-white"></i>
                                </div>
                                <div>
                                    <h4 class="font-semibold text-gray-800 text-sm">喜马拉雅VIP会员</h4>
                                    <p class="text-xs text-purple-600">使用中 · 剩余15天</p>
                                </div>
                            </div>
                            <button class="bg-purple-600 text-white px-3 py-1 rounded-lg text-xs">续费</button>
                        </div>
                    </div>

                    <!-- 爱奇艺VIP -->
                    <div class="border border-red-200 rounded-xl p-3 bg-red-50">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-red-500 rounded-lg flex items-center justify-center mr-3">
                                    <i class="fas fa-play text-white"></i>
                                </div>
                                <div>
                                    <h4 class="font-semibold text-gray-800 text-sm">爱奇艺视频VIP</h4>
                                    <p class="text-xs text-red-600">待续费 · 3天后到期</p>
                                </div>
                            </div>
                            <button class="bg-red-500 text-white px-3 py-1 rounded-lg text-xs">续费</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 分类导航 -->
        <div class="mx-4 mb-6">
            <div class="flex device-card rounded-2xl p-1">
                <button class="flex-1 py-3 px-4 bg-blue-500 text-white font-medium rounded-xl">
                    数字化商品
                </button>
                <button class="flex-1 py-3 px-4 text-gray-600 font-medium rounded-xl">
                    智能设备
                </button>
            </div>
        </div>

        <!-- 流量套餐区 -->
        <div class="mx-4 mb-6">
            <h3 class="text-lg font-semibold text-gray-800 mb-4">流量套餐</h3>

            <!-- 基础套餐 -->
            <div class="device-card rounded-2xl p-4 mb-4">
                <h4 class="font-semibold text-gray-800 mb-3 flex items-center">
                    <i class="fas fa-sim-card text-blue-500 mr-2"></i>
                    基础套餐
                </h4>
                <div class="space-y-3">
                    <div class="border border-blue-200 rounded-xl p-3 bg-blue-50">
                        <div class="flex justify-between items-center mb-2">
                            <span class="font-semibold text-blue-800">5G畅享套餐</span>
                            <span class="text-blue-600 font-bold">¥99/月</span>
                        </div>
                        <p class="text-sm text-blue-600">100GB流量 + 无限通话</p>
                        <div class="flex justify-between items-center mt-2">
                            <span class="text-xs text-blue-500">支持1/3/12个月购买</span>
                            <button class="bg-blue-500 text-white px-3 py-1 rounded-lg text-sm">选择</button>
                        </div>
                    </div>

                    <div class="border border-gray-200 rounded-xl p-3">
                        <div class="flex justify-between items-center mb-2">
                            <span class="font-semibold text-gray-800">5G标准套餐</span>
                            <span class="text-gray-800 font-bold">¥69/月</span>
                        </div>
                        <p class="text-sm text-gray-600">50GB流量 + 500分钟通话</p>
                        <div class="flex justify-between items-center mt-2">
                            <span class="text-xs text-gray-500">支持1/3/12个月购买</span>
                            <button class="border border-gray-300 text-gray-600 px-3 py-1 rounded-lg text-sm">选择</button>
                        </div>
                    </div>

                    <div class="border border-gray-200 rounded-xl p-3">
                        <div class="flex justify-between items-center mb-2">
                            <span class="font-semibold text-gray-800">5G基础套餐</span>
                            <span class="text-gray-800 font-bold">¥39/月</span>
                        </div>
                        <p class="text-sm text-gray-600">20GB流量 + 200分钟通话</p>
                        <div class="flex justify-between items-center mt-2">
                            <span class="text-xs text-gray-500">支持1/3/12个月购买</span>
                            <button class="border border-gray-300 text-gray-600 px-3 py-1 rounded-lg text-sm">选择</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 叠加套餐 -->
            <div class="device-card rounded-2xl p-4">
                <h4 class="font-semibold text-gray-800 mb-3 flex items-center">
                    <i class="fas fa-plus-circle text-green-500 mr-2"></i>
                    叠加套餐
                </h4>
                <div class="grid grid-cols-3 gap-3">
                    <div class="border border-green-200 rounded-xl p-3 text-center bg-green-50">
                        <p class="font-bold text-green-800">10GB</p>
                        <p class="text-sm text-green-600 mb-2">¥19</p>
                        <button class="bg-green-500 text-white px-2 py-1 rounded text-xs w-full">购买</button>
                    </div>
                    <div class="border border-gray-200 rounded-xl p-3 text-center">
                        <p class="font-bold text-gray-800">20GB</p>
                        <p class="text-sm text-gray-600 mb-2">¥35</p>
                        <button class="border border-gray-300 text-gray-600 px-2 py-1 rounded text-xs w-full">购买</button>
                    </div>
                    <div class="border border-gray-200 rounded-xl p-3 text-center">
                        <p class="font-bold text-gray-800">30GB</p>
                        <p class="text-sm text-gray-600 mb-2">¥49</p>
                        <button class="border border-gray-300 text-gray-600 px-2 py-1 rounded text-xs w-full">购买</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 内容服务区 -->
        <div class="mx-4 mb-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-800">内容服务</h3>
                <button class="text-blue-500 text-sm font-medium flex items-center">
                    <span>更多</span>
                    <i class="fas fa-chevron-right ml-1 text-xs"></i>
                </button>
            </div>

            <!-- 热门推荐 -->
            <div class="device-card rounded-2xl p-4">
                <h4 class="font-semibold text-gray-800 mb-3 flex items-center">
                    <i class="fas fa-fire text-red-500 mr-2"></i>
                    热门推荐
                </h4>
                <div class="space-y-3">
                    <!-- 得到年度会员 -->
                    <div class="border border-orange-200 rounded-xl p-3 bg-orange-50">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="w-10 h-10 rounded-lg flex items-center justify-center mr-3" style="background-color: #f97316;">
                                    <i class="fas fa-book text-white text-lg"></i>
                                </div>
                                <div>
                                    <h5 class="font-semibold text-gray-800 text-sm">得到年度会员</h5>
                                    <p class="text-xs text-orange-600">知识付费 · 精品课程</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <p class="text-orange-600 font-bold text-sm">¥199</p>
                                <button class="text-white px-3 py-1 rounded-lg text-xs mt-1" style="background-color: #f97316;">购买</button>
                            </div>
                        </div>
                    </div>

                    <!-- 喜马拉雅VIP -->
                    <div class="border border-purple-200 rounded-xl p-3 bg-purple-50">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-purple-600 rounded-lg flex items-center justify-center mr-3">
                                    <i class="fas fa-headphones text-white"></i>
                                </div>
                                <div>
                                    <h5 class="font-semibold text-gray-800 text-sm">喜马拉雅VIP会员</h5>
                                    <p class="text-xs text-purple-600">有声内容 · 海量资源</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <p class="text-purple-600 font-bold text-sm">¥158</p>
                                <button class="bg-purple-600 text-white px-3 py-1 rounded-lg text-xs mt-1">购买</button>
                            </div>
                        </div>
                    </div>

                    <!-- QQ音乐绿钻 -->
                    <div class="border border-green-200 rounded-xl p-3 bg-green-50">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-green-500 rounded-lg flex items-center justify-center mr-3">
                                    <i class="fas fa-music text-white"></i>
                                </div>
                                <div>
                                    <h5 class="font-semibold text-gray-800 text-sm">QQ音乐绿钻会员</h5>
                                    <p class="text-xs text-green-600">音乐服务 · 无损音质</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <p class="text-green-600 font-bold text-sm">¥88</p>
                                <button class="bg-green-500 text-white px-3 py-1 rounded-lg text-xs mt-1">购买</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 智能设备推荐 -->
        <div class="mx-4 mb-8">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-800">智能设备推荐</h3>
                <button class="text-blue-500 text-sm font-medium flex items-center">
                    <span>更多</span>
                    <i class="fas fa-chevron-right ml-1 text-xs"></i>
                </button>
            </div>
            <div class="grid grid-cols-2 gap-3">
                <div class="device-card rounded-2xl p-3">
                    <img src="https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=200&h=150&fit=crop"
                         alt="5G CPE" class="w-full h-24 object-cover rounded-xl mb-2">
                    <h4 class="font-semibold text-gray-800 text-sm">5G CPE</h4>
                    <p class="text-xs text-gray-500 mb-2">高速5G网络设备</p>
                    <div class="flex justify-between items-center">
                        <span class="text-blue-600 font-bold text-sm">¥599</span>
                        <button class="bg-blue-500 text-white px-2 py-1 rounded text-xs">购买</button>
                    </div>
                </div>

                <div class="device-card rounded-2xl p-3">
                    <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=200&h=150&fit=crop"
                         alt="智能音箱" class="w-full h-24 object-cover rounded-xl mb-2">
                    <h4 class="font-semibold text-gray-800 text-sm">智能音箱Pro</h4>
                    <p class="text-xs text-gray-500 mb-2">AI语音助手</p>
                    <div class="flex justify-between items-center">
                        <span class="text-blue-600 font-bold text-sm">¥299</span>
                        <button class="bg-blue-500 text-white px-2 py-1 rounded text-xs">购买</button>
                    </div>
                </div>

                <div class="device-card rounded-2xl p-3">
                    <img src="https://images.unsplash.com/photo-1621905251189-08b45d6a269e?w=200&h=150&fit=crop"
                         alt="智能开关" class="w-full h-24 object-cover rounded-xl mb-2">
                    <h4 class="font-semibold text-gray-800 text-sm">智能开关</h4>
                    <p class="text-xs text-gray-500 mb-2">远程控制开关</p>
                    <div class="flex justify-between items-center">
                        <span class="text-blue-600 font-bold text-sm">¥89</span>
                        <button class="bg-blue-500 text-white px-2 py-1 rounded text-xs">购买</button>
                    </div>
                </div>

                <div class="device-card rounded-2xl p-3">
                    <img src="https://images.unsplash.com/photo-1586953208448-b95a79798f07?w=200&h=150&fit=crop"
                         alt="智能锁" class="w-full h-24 object-cover rounded-xl mb-2">
                    <h4 class="font-semibold text-gray-800 text-sm">智能门锁</h4>
                    <p class="text-xs text-gray-500 mb-2">指纹密码解锁</p>
                    <div class="flex justify-between items-center">
                        <span class="text-blue-600 font-bold text-sm">¥899</span>
                        <button class="bg-blue-500 text-white px-2 py-1 rounded text-xs">购买</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 底部安全间距 -->
        <div class="h-6"></div>
    </div>

    <!-- 底部导航栏 -->
    <div class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200">
        <div class="flex justify-around py-2">
            <button class="flex flex-col items-center py-2 px-3">
                <i class="fas fa-home text-gray-400 text-lg mb-1"></i>
                <span class="text-xs text-gray-400">首页</span>
            </button>
            <button class="flex flex-col items-center py-2 px-3">
                <i class="fas fa-layer-group text-gray-400 text-lg mb-1"></i>
                <span class="text-xs text-gray-400">场景</span>
            </button>
            <button class="flex flex-col items-center py-2 px-3">
                <i class="fas fa-robot text-gray-400 text-lg mb-1"></i>
                <span class="text-xs text-gray-400">AI智能体</span>
            </button>
            <button class="flex flex-col items-center py-2 px-3">
                <i class="fas fa-store text-blue-500 text-lg mb-1"></i>
                <span class="text-xs text-blue-500 font-medium">商城</span>
            </button>
            <button class="flex flex-col items-center py-2 px-3">
                <i class="fas fa-user text-gray-400 text-lg mb-1"></i>
                <span class="text-xs text-gray-400">我的</span>
            </button>
        </div>
    </div>

    <!-- iOS底部指示器 -->
    <div class="fixed bottom-0 left-0 right-0 flex justify-center pt-4 pb-2 bg-transparent">
        <div class="w-32 h-1 bg-black rounded-full"></div>
    </div>
</body>
</html>
