<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商城相关页面 - 合觅App原型UE设计</title>
    <link href="https://unpkg.com/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            background-color: #f6f6f6;
        }
        .iframe-container {
            background-color: #f6f6f6;
        }
        iframe {
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            max-width: 100%;
        }

        /* 响应式iframe尺寸 */
        @media (min-width: 1024px) {
            .responsive-iframe {
                width: 393px;
                height: 852px;
            }
        }

        @media (max-width: 1023px) and (min-width: 768px) {
            .responsive-iframe {
                width: 280px;
                height: 607px;
            }
        }

        @media (max-width: 767px) {
            .responsive-iframe {
                width: 100%;
                max-width: 393px;
                height: 852px;
            }
        }
    </style>
</head>
<body class="min-h-screen p-2 md:p-6 lg:p-8">
    <div class="max-w-7xl mx-auto">
        <!-- 页面标题 -->
        <div class="text-center mb-8">
            <h1 class="text-2xl md:text-3xl lg:text-4xl font-bold text-gray-800">商城</h1>
        </div>

        <!-- 返回按钮 -->
        <div class="mb-8">
            <button onclick="window.location.href='../index.html'" class="flex items-center text-blue-600 hover:text-blue-800">
                <i class="fas fa-arrow-left mr-2"></i>
                <span class="text-lg font-medium">返回主页</span>
            </button>
        </div>

        <!-- 暂无内容提示 -->
        <div class="text-center py-16">
            <i class="fas fa-shopping-cart text-6xl text-gray-300 mb-4"></i>
            <h2 class="text-2xl font-bold text-gray-500 mb-2">暂无商城相关页面</h2>
            <p class="text-gray-400">商城功能的二级页面将在此处展示</p>
        </div>
    </div>
</body>
</html>
