<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>我的 - 合觅App</title>
    <link href="https://unpkg.com/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body { overflow-y: auto; overflow-x: hidden; }
        ::-webkit-scrollbar { display: none; }

    </style>
</head>
<body class="bg-gray-50 font-sans min-h-screen flex flex-col">
    <!-- iOS状态栏 -->
    <div class="fixed top-0 left-0 right-0 bg-gray-50 text-black text-sm px-4 py-2 flex justify-between items-center z-50">
        <div class="font-semibold">
            9:41
        </div>
        <div class="flex items-center space-x-1">
            <i class="fas fa-signal text-xs"></i>
            <i class="fas fa-wifi text-xs"></i>
            <i class="fas fa-battery-full text-xs"></i>
        </div>
    </div>

    <!-- 主内容区域 -->
    <div class="flex-1 overflow-y-auto pt-12 pb-20">
        <!-- 顶部导航 -->
        <div class="flex justify-between items-center p-4">
            <div>
                <h1 class="text-2xl font-bold text-gray-800">我的</h1>
                <p class="text-sm text-gray-600">个人中心与设置</p>
            </div>
            <div class="flex space-x-4">
                <button class="p-3 bg-white bg-opacity-60 rounded-full">
                    <i class="fas fa-cog text-gray-600"></i>
                </button>
            </div>
        </div>

        <!-- 主要内容 -->
        <div class="px-4 mb-4 space-y-6">
        <!-- 用户信息卡片 -->
        <div class="bg-white p-6 rounded-xl shadow-sm">
            <div class="flex items-center space-x-4">
                <!-- 用户头像 -->
                <div class="relative">
                    <div class="w-16 h-16 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
                        <i class="fas fa-user text-white text-2xl"></i>
                    </div>
                    <div class="absolute -bottom-1 -right-1 w-6 h-6 bg-green-500 rounded-full border-2 border-white flex items-center justify-center">
                        <i class="fas fa-check text-white text-xs"></i>
                    </div>
                </div>
                <!-- 用户信息 -->
                <div class="flex-1">
                    <h2 class="text-lg font-semibold text-gray-800">合觅</h2>
                    <p class="text-gray-600 text-sm">手机号：138****8888</p>
                    <p class="text-green-600 text-xs mt-1">
                        <i class="fas fa-shield-alt mr-1"></i>
                        已实名认证
                    </p>
                </div>
                <!-- 编辑按钮 -->
                <button class="p-2 bg-gray-100 rounded-full">
                    <i class="fas fa-edit text-gray-600"></i>
                </button>
            </div>
        </div>

        <!-- 商城服务 -->
        <div class="bg-white rounded-xl shadow-sm overflow-hidden">
            <div class="px-4 py-3 bg-gray-50 border-b border-gray-100">
                <h3 class="text-sm font-semibold text-gray-700">商城服务</h3>
            </div>

            <!-- 订单管理 -->
            <div class="flex items-center justify-between p-4 border-b border-gray-100">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                        <i class="fas fa-receipt text-blue-600"></i>
                    </div>
                    <div>
                        <h3 class="font-medium text-gray-800">我的订单</h3>
                        <p class="text-sm text-gray-600">查看订单状态与历史</p>
                    </div>
                </div>
                <i class="fas fa-chevron-right text-gray-400"></i>
            </div>
        </div>

        <!-- 设备管理 -->
        <div class="bg-white rounded-xl shadow-sm overflow-hidden">
            <div class="px-4 py-3 bg-gray-50 border-b border-gray-100">
                <h3 class="text-sm font-semibold text-gray-700">设备管理</h3>
            </div>

            <!-- 场景设置 -->
            <div class="flex items-center justify-between p-4 border-b border-gray-100">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center">
                        <i class="fas fa-home text-purple-600"></i>
                    </div>
                    <div>
                        <h3 class="font-medium text-gray-800">场景设置</h3>
                        <p class="text-sm text-gray-600">智能场景配置</p>
                    </div>
                </div>
                <i class="fas fa-chevron-right text-gray-400"></i>
            </div>

            <!-- 设备分享 -->
            <div class="flex items-center justify-between p-4">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                        <i class="fas fa-share-alt text-green-600"></i>
                    </div>
                    <div>
                        <h3 class="font-medium text-gray-800">设备分享</h3>
                        <p class="text-sm text-gray-600">与家人共享设备</p>
                    </div>
                </div>
                <i class="fas fa-chevron-right text-gray-400"></i>
            </div>
        </div>

        <!-- 应用设置 -->
        <div class="bg-white rounded-xl shadow-sm overflow-hidden">
            <div class="px-4 py-3 bg-gray-50 border-b border-gray-100">
                <h3 class="text-sm font-semibold text-gray-700">应用设置</h3>
            </div>

            <!-- 声纹管理 -->
            <div class="flex items-center justify-between p-4 border-b border-gray-100">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center">
                        <i class="fas fa-microphone text-purple-600"></i>
                    </div>
                    <div>
                        <h3 class="font-medium text-gray-800">声纹管理</h3>
                        <p class="text-sm text-gray-600">语音识别与声纹配置</p>
                    </div>
                </div>
                <i class="fas fa-chevron-right text-gray-400"></i>
            </div>

            <!-- 方言与音色 -->
            <div class="flex items-center justify-between p-4">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-orange-100 rounded-full flex items-center justify-center">
                        <i class="fas fa-language text-orange-600"></i>
                    </div>
                    <div>
                        <h3 class="font-medium text-gray-800">方言与音色</h3>
                        <p class="text-sm text-gray-600">语音方言识别与音色设置</p>
                    </div>
                </div>
                <i class="fas fa-chevron-right text-gray-400"></i>
            </div>
        </div>

        <!-- 帮助与支持 -->
        <div class="bg-white rounded-xl shadow-sm overflow-hidden">
            <!-- 帮助与支持区域标题 -->
            <div class="px-4 py-3 bg-gray-50 border-b border-gray-100">
                <h3 class="text-sm font-semibold text-gray-700">帮助与支持</h3>
            </div>

            <!-- 使用说明 -->
            <div class="flex items-center justify-between p-4 border-b border-gray-100">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-yellow-100 rounded-full flex items-center justify-center">
                        <i class="fas fa-question-circle text-yellow-600"></i>
                    </div>
                    <div>
                        <h3 class="font-medium text-gray-800">使用说明</h3>
                        <p class="text-sm text-gray-600">操作指南与帮助</p>
                    </div>
                </div>
                <i class="fas fa-chevron-right text-gray-400"></i>
            </div>

            <!-- 联系客服 -->
            <div class="flex items-center justify-between p-4">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                        <i class="fas fa-headset text-blue-600"></i>
                    </div>
                    <div>
                        <h3 class="font-medium text-gray-800">联系客服</h3>
                        <p class="text-sm text-gray-600">在线客服与技术支持</p>
                    </div>
                </div>
                <i class="fas fa-chevron-right text-gray-400"></i>
            </div>
        </div>

        <!-- 法律信息 -->
        <div class="bg-white rounded-xl shadow-sm overflow-hidden">
            <div class="px-4 py-3 bg-gray-50 border-b border-gray-100">
                <h3 class="text-sm font-semibold text-gray-700">法律信息</h3>
            </div>

            <!-- 服务协议 -->
            <div class="flex items-center justify-between p-4 border-b border-gray-100">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-red-100 rounded-full flex items-center justify-center">
                        <i class="fas fa-file-contract text-red-600"></i>
                    </div>
                    <div>
                        <h3 class="font-medium text-gray-800">服务协议</h3>
                        <p class="text-sm text-gray-600">用户协议与隐私政策</p>
                    </div>
                </div>
                <i class="fas fa-chevron-right text-gray-400"></i>
            </div>

            <!-- 关于我们 -->
            <div class="flex items-center justify-between p-4">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-indigo-100 rounded-full flex items-center justify-center">
                        <i class="fas fa-info-circle text-indigo-600"></i>
                    </div>
                    <div>
                        <h3 class="font-medium text-gray-800">关于我们</h3>
                        <p class="text-sm text-gray-600">应用信息与版本</p>
                    </div>
                </div>
                <i class="fas fa-chevron-right text-gray-400"></i>
            </div>
        </div>



        <!-- 退出登录按钮 -->
        <div class="mx-4 mt-12">
            <div class="w-full bg-red-500 text-white py-4 rounded-xl font-medium text-base text-center">
                退出登录
            </div>
        </div>
        </div>
    </div>

    <!-- 底部导航栏 -->
    <div class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200">
        <div class="flex justify-around py-2">
            <button class="flex flex-col items-center py-2 px-3">
                <i class="fas fa-home text-gray-400 text-lg mb-1"></i>
                <span class="text-xs text-gray-400">首页</span>
            </button>
            <button class="flex flex-col items-center py-2 px-3">
                <i class="fas fa-layer-group text-gray-400 text-lg mb-1"></i>
                <span class="text-xs text-gray-400">场景</span>
            </button>
            <button class="flex flex-col items-center py-2 px-3">
                <i class="fas fa-robot text-gray-400 text-lg mb-1"></i>
                <span class="text-xs text-gray-400">AI智能体</span>
            </button>
            <button class="flex flex-col items-center py-2 px-3">
                <i class="fas fa-store text-gray-400 text-lg mb-1"></i>
                <span class="text-xs text-gray-400">商城</span>
            </button>
            <button class="flex flex-col items-center py-2 px-3">
                <i class="fas fa-user text-blue-500 text-lg mb-1"></i>
                <span class="text-xs text-blue-500 font-medium">我的</span>
            </button>
        </div>
    </div>

    <!-- iOS底部指示器 -->
    <div class="fixed bottom-0 left-0 right-0 flex justify-center pt-4 pb-2 bg-transparent">
        <div class="w-32 h-1 bg-black rounded-full"></div>
    </div>

    <!-- 底部安全区域 -->
    <div class="h-20"></div>
</body>
</html>
