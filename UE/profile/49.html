<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>全部订单 - 合觅</title>
    <link href="https://unpkg.com/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            background: linear-gradient(180deg, #e6f3ff 0%, #f0f8ff 100%);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        .device-card {
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .tab-active {
            color: #3b82f6;
            border-bottom: 2px solid #3b82f6;
        }
        .empty-box {
            width: 80px;
            height: 60px;
            background: #e5e7eb;
            border-radius: 8px;
            position: relative;
            margin: 0 auto 16px;
        }
        .empty-box::before {
            content: '';
            position: absolute;
            top: -8px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 4px;
            background: #d1d5db;
            border-radius: 2px;
        }
        .empty-box::after {
            content: '';
            position: absolute;
            top: -12px;
            left: 50%;
            transform: translateX(-50%);
            width: 40px;
            height: 4px;
            background: #d1d5db;
            border-radius: 2px;
        }
    </style>
</head>
<body class="min-h-screen">
    <!-- iOS状态栏 -->
    <div class="fixed top-0 left-0 right-0 text-black text-sm px-4 py-2 flex justify-between items-center z-50" style="background: linear-gradient(180deg, #e6f3ff 0%, #f0f8ff 100%);">
        <div class="font-semibold">
            9:41
        </div>
        <div class="flex items-center space-x-1">
            <i class="fas fa-signal text-xs"></i>
            <i class="fas fa-wifi text-xs"></i>
            <i class="fas fa-battery-full text-xs"></i>
        </div>
    </div>

    <!-- 顶部导航 -->
    <div class="fixed top-10 left-0 right-0 bg-transparent px-4 py-3 z-40">
        <div class="flex items-center justify-center relative">
            <button class="absolute left-0">
                <i class="fas fa-chevron-left text-gray-600 text-lg"></i>
            </button>
            <h1 class="text-lg font-semibold text-gray-800">全部订单</h1>
            <button class="absolute right-0 p-2">
                <i class="fas fa-search text-gray-600"></i>
            </button>
        </div>
    </div>

    <!-- 订单状态标签 -->
    <div class="fixed top-24 left-0 right-0 bg-white bg-opacity-80 backdrop-filter backdrop-blur-sm px-4 py-3 z-30">
        <div class="flex space-x-6 overflow-x-auto">
            <button class="tab-active whitespace-nowrap pb-2 text-sm font-medium">全部</button>
            <button class="text-gray-500 whitespace-nowrap pb-2 text-sm font-medium">待支付</button>
            <button class="text-gray-500 whitespace-nowrap pb-2 text-sm font-medium">待发货</button>
            <button class="text-gray-500 whitespace-nowrap pb-2 text-sm font-medium">待收货</button>
            <button class="text-gray-500 whitespace-nowrap pb-2 text-sm font-medium">交易成功</button>
        </div>
    </div>

    <!-- 主内容区域 -->
    <div class="pt-40 pb-20 px-4">
        <!-- 订单列表 -->
        <div class="space-y-4">
            <!-- 订单1: 5G CPE设备 -->
            <div class="device-card rounded-2xl p-4">
                <div class="flex items-center justify-between mb-3">
                    <div class="flex items-center">
                        <i class="fas fa-store text-gray-500 text-sm mr-2"></i>
                        <span class="text-sm text-gray-600">合觅官方旗舰店</span>
                    </div>
                    <span class="text-sm text-green-600 font-medium">交易成功</span>
                </div>
                
                <div class="flex items-center space-x-3 mb-3">
                    <div class="w-16 h-16 bg-blue-100 rounded-lg flex items-center justify-center">
                        <i class="fa-solid fa-charging-station text-blue-500 text-xl"></i>
                    </div>
                    <div class="flex-1">
                        <h3 class="font-medium text-gray-800 mb-1">5G CPE P3 Pro</h3>
                        <p class="text-sm text-gray-500 mb-1">颜色: 白色 | 套餐: 标准版</p>
                        <p class="text-sm text-gray-500">数量: 1</p>
                    </div>
                    <div class="text-right">
                        <p class="text-lg font-semibold text-gray-800">¥899</p>
                    </div>
                </div>
                
                <div class="flex items-center justify-between pt-3 border-t border-gray-100">
                    <span class="text-sm text-gray-500">订单号: 202312150001</span>
                    <div class="flex space-x-2">
                        <button class="bg-gray-100 text-gray-600 px-3 py-1 rounded-lg text-sm">再次购买</button>
                        <button class="bg-blue-500 text-white px-3 py-1 rounded-lg text-sm">查看详情</button>
                    </div>
                </div>
            </div>

            <!-- 订单2: 喜马拉雅VIP -->
            <div class="device-card rounded-2xl p-4">
                <div class="flex items-center justify-between mb-3">
                    <div class="flex items-center">
                        <i class="fas fa-store text-gray-500 text-sm mr-2"></i>
                        <span class="text-sm text-gray-600">喜马拉雅官方</span>
                    </div>
                    <span class="text-sm text-green-600 font-medium">交易成功</span>
                </div>
                
                <div class="flex items-center space-x-3 mb-3">
                    <div class="w-16 h-16 bg-purple-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-headphones text-purple-600 text-xl"></i>
                    </div>
                    <div class="flex-1">
                        <h3 class="font-medium text-gray-800 mb-1">喜马拉雅VIP会员</h3>
                        <p class="text-sm text-gray-500 mb-1">套餐: 年度会员</p>
                        <p class="text-sm text-gray-500">有效期: 12个月</p>
                    </div>
                    <div class="text-right">
                        <p class="text-lg font-semibold text-gray-800">¥198</p>
                    </div>
                </div>
                
                <div class="flex items-center justify-between pt-3 border-t border-gray-100">
                    <span class="text-sm text-gray-500">订单号: 202312100002</span>
                    <div class="flex space-x-2">
                        <button class="bg-gray-100 text-gray-600 px-3 py-1 rounded-lg text-sm">再次购买</button>
                        <button class="bg-blue-500 text-white px-3 py-1 rounded-lg text-sm">查看详情</button>
                    </div>
                </div>
            </div>

            <!-- 订单3: 5G流量套餐 -->
            <div class="device-card rounded-2xl p-4">
                <div class="flex items-center justify-between mb-3">
                    <div class="flex items-center">
                        <i class="fas fa-store text-gray-500 text-sm mr-2"></i>
                        <span class="text-sm text-gray-600">中国移动官方</span>
                    </div>
                    <span class="text-sm text-green-600 font-medium">交易成功</span>
                </div>
                
                <div class="flex items-center space-x-3 mb-3">
                    <div class="w-16 h-16 bg-green-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-sim-card text-green-600 text-xl"></i>
                    </div>
                    <div class="flex-1">
                        <h3 class="font-medium text-gray-800 mb-1">5G畅享套餐</h3>
                        <p class="text-sm text-gray-500 mb-1">流量: 100GB | 通话: 1000分钟</p>
                        <p class="text-sm text-gray-500">有效期: 1个月</p>
                    </div>
                    <div class="text-right">
                        <p class="text-lg font-semibold text-gray-800">¥128</p>
                    </div>
                </div>
                
                <div class="flex items-center justify-between pt-3 border-t border-gray-100">
                    <span class="text-sm text-gray-500">订单号: 202312050003</span>
                    <div class="flex space-x-2">
                        <button class="bg-gray-100 text-gray-600 px-3 py-1 rounded-lg text-sm">再次购买</button>
                        <button class="bg-blue-500 text-white px-3 py-1 rounded-lg text-sm">查看详情</button>
                    </div>
                </div>
            </div>

            <!-- 订单4: 爱奇艺VIP -->
            <div class="device-card rounded-2xl p-4">
                <div class="flex items-center justify-between mb-3">
                    <div class="flex items-center">
                        <i class="fas fa-store text-gray-500 text-sm mr-2"></i>
                        <span class="text-sm text-gray-600">爱奇艺官方</span>
                    </div>
                    <span class="text-sm text-green-600 font-medium">交易成功</span>
                </div>

                <div class="flex items-center space-x-3 mb-3">
                    <div class="w-16 h-16 bg-red-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-play text-red-600 text-xl"></i>
                    </div>
                    <div class="flex-1">
                        <h3 class="font-medium text-gray-800 mb-1">爱奇艺视频VIP</h3>
                        <p class="text-sm text-gray-500 mb-1">套餐: 季度会员</p>
                        <p class="text-sm text-gray-500">有效期: 3个月</p>
                    </div>
                    <div class="text-right">
                        <p class="text-lg font-semibold text-gray-800">¥58</p>
                    </div>
                </div>

                <div class="flex items-center justify-between pt-3 border-t border-gray-100">
                    <span class="text-sm text-gray-500">订单号: 202311280004</span>
                    <div class="flex space-x-2">
                        <button class="bg-gray-100 text-gray-600 px-3 py-1 rounded-lg text-sm">再次购买</button>
                        <button class="bg-blue-500 text-white px-3 py-1 rounded-lg text-sm">查看详情</button>
                    </div>
                </div>
            </div>

            <!-- 订单5: QQ音乐VIP -->
            <div class="device-card rounded-2xl p-4">
                <div class="flex items-center justify-between mb-3">
                    <div class="flex items-center">
                        <i class="fas fa-store text-gray-500 text-sm mr-2"></i>
                        <span class="text-sm text-gray-600">QQ音乐官方</span>
                    </div>
                    <span class="text-sm text-green-600 font-medium">交易成功</span>
                </div>

                <div class="flex items-center space-x-3 mb-3">
                    <div class="w-16 h-16 bg-green-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-music text-green-600 text-xl"></i>
                    </div>
                    <div class="flex-1">
                        <h3 class="font-medium text-gray-800 mb-1">QQ音乐VIP会员</h3>
                        <p class="text-sm text-gray-500 mb-1">套餐: 月度会员</p>
                        <p class="text-sm text-gray-500">有效期: 1个月</p>
                    </div>
                    <div class="text-right">
                        <p class="text-lg font-semibold text-gray-800">¥15</p>
                    </div>
                </div>

                <div class="flex items-center justify-between pt-3 border-t border-gray-100">
                    <span class="text-sm text-gray-500">订单号: 202311200005</span>
                    <div class="flex space-x-2">
                        <button class="bg-gray-100 text-gray-600 px-3 py-1 rounded-lg text-sm">再次购买</button>
                        <button class="bg-blue-500 text-white px-3 py-1 rounded-lg text-sm">查看详情</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- iOS底部指示器 -->
    <div class="fixed bottom-0 left-0 right-0 flex justify-center pt-4 pb-2 bg-transparent">
        <div class="w-32 h-1 bg-black rounded-full"></div>
    </div>
</body>
</html>
