<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>添加声纹 - 合觅</title>
    <link href="https://unpkg.com/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            background: linear-gradient(180deg, #e6f3ff 0%, #f0f8ff 100%);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        .device-card {
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            font-weight: bold;
        }
        .avatar-female {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }
        .chat-bubble {
            background: rgba(59, 130, 246, 0.1);
            border: 1px solid rgba(59, 130, 246, 0.2);
            border-radius: 12px;
            padding: 12px 16px;
            margin-bottom: 8px;
            position: relative;
        }
        .chat-bubble::after {
            content: '';
            position: absolute;
            bottom: -8px;
            left: 20px;
            width: 0;
            height: 0;
            border-left: 8px solid transparent;
            border-right: 8px solid transparent;
            border-top: 8px solid rgba(59, 130, 246, 0.1);
        }
    </style>
</head>
<body class="h-screen">
    <!-- iOS状态栏 -->
    <div class="fixed top-0 left-0 right-0 text-black text-sm px-4 py-2 flex justify-between items-center z-50" style="background: linear-gradient(180deg, #e6f3ff 0%, #f0f8ff 100%);">
        <div class="font-semibold">
            9:41
        </div>
        <div class="flex items-center space-x-1">
            <i class="fas fa-signal text-xs"></i>
            <i class="fas fa-wifi text-xs"></i>
            <i class="fas fa-battery-full text-xs"></i>
        </div>
    </div>

    <!-- 顶部导航 -->
    <div class="fixed top-10 left-0 right-0 bg-transparent px-4 py-3 z-40">
        <div class="flex items-center justify-center relative">
            <button class="absolute left-0">
                <i class="fas fa-chevron-left text-gray-600 text-lg"></i>
            </button>
            <h1 class="text-lg font-semibold text-gray-800">添加声纹</h1>
        </div>
    </div>

    <!-- 主内容区域 -->
    <div class="flex-1 overflow-y-auto pt-20 pb-32 px-4">
        <!-- 对话场景 -->
        <div class="mt-8 mb-8">
            <div class="flex items-start space-x-4 mb-6">
                <!-- 小明头像 -->
                <div class="flex flex-col items-center">
                    <div class="avatar">
                        小明
                    </div>
                    <p class="text-sm text-gray-600 mt-2">小明</p>
                </div>
                
                <!-- 对话气泡 -->
                <div class="flex-1">
                    <div class="chat-bubble">
                        <p class="text-gray-700 text-sm">小合同学，今天有什么安排</p>
                    </div>
                </div>
                
                <!-- 小红头像 -->
                <div class="flex flex-col items-center">
                    <div class="avatar avatar-female">
                        小红
                    </div>
                    <p class="text-sm text-gray-600 mt-2">小红</p>
                </div>
            </div>
        </div>

        <!-- 说明文字 -->
        <div class="device-card rounded-2xl p-6 mb-8">
            <p class="text-gray-600 text-sm leading-relaxed text-center">
                录制声纹后，您可以在音箱、电视等设备享受个性化服务，同时您还可以邀请家人在您的设备上使用声纹
            </p>
        </div>
    </div>

    <!-- 底部按钮 -->
    <div class="fixed bottom-0 left-0 right-0 p-4 bg-transparent" style="bottom: 32px;">
        <button class="w-full bg-blue-500 text-white py-3 rounded-xl font-medium text-base">
            开启我的声纹
        </button>
    </div>

    <!-- iOS底部指示器 -->
    <div class="fixed bottom-0 left-0 right-0 flex justify-center pt-4 pb-2 bg-transparent">
        <div class="w-32 h-1 bg-black rounded-full"></div>
    </div>
</body>
</html>
