<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>我的页面相关 - 合觅App原型UE设计</title>
    <link href="https://unpkg.com/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            background-color: #f6f6f6;
        }
        .iframe-container {
            background-color: #f6f6f6;
        }
        iframe {
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            max-width: 100%;
        }

        /* 响应式iframe尺寸 */
        @media (min-width: 1024px) {
            .responsive-iframe {
                width: 393px;
                height: 852px;
            }
        }

        @media (max-width: 1023px) and (min-width: 768px) {
            .responsive-iframe {
                width: 280px;
                height: 607px;
            }
        }

        @media (max-width: 767px) {
            .responsive-iframe {
                width: 100%;
                max-width: 393px;
                height: 852px;
            }
        }
    </style>
</head>
<body class="min-h-screen p-2 md:p-6 lg:p-8">
    <div class="max-w-7xl mx-auto">
        <!-- 页面标题 -->
        <div class="text-center mb-8">
            <h1 class="text-2xl md:text-3xl lg:text-4xl font-bold text-gray-800">我的</h1>
        </div>

        <!-- 返回按钮 -->
        <div class="mb-8">
            <button onclick="window.location.href='../index.html'" class="flex items-center text-blue-600 hover:text-blue-800">
                <i class="fas fa-arrow-left mr-2"></i>
                <span class="text-lg font-medium">返回主页</span>
            </button>
        </div>

        <!-- 个人功能相关页面 -->
        <div class="mb-8">
            <h2 class="text-2xl font-bold text-gray-800 mb-6 text-center">个人功能</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2 md:gap-6 lg:gap-8 iframe-container">
                <!-- 声纹管理 -->
                <div class="text-center">
                    <h3 class="text-lg md:text-xl font-semibold text-gray-700 mb-2">声纹管理</h3>
                    <iframe src="52.html" class="responsive-iframe mx-auto mb-2"></iframe>
                    <p class="text-sm text-gray-600"><span class="text-blue-600 cursor-pointer hover:text-blue-800 hover:underline" onclick="window.location.href='52.html'">52.html</span></p>
                </div>

                <!-- 我的订单 -->
                <div class="text-center">
                    <h3 class="text-lg md:text-xl font-semibold text-gray-700 mb-2">我的订单</h3>
                    <iframe src="49.html" class="responsive-iframe mx-auto mb-2"></iframe>
                    <p class="text-sm text-gray-600"><span class="text-blue-600 cursor-pointer hover:text-blue-800 hover:underline" onclick="window.location.href='49.html'">49.html</span></p>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
