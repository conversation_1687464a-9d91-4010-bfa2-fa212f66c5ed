<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>场景</title>
    <link href="https://unpkg.com/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            background: linear-gradient(180deg, #e6f3ff 0%, #f0f8ff 50%, #e8f4f8 100%);
            overflow-x: hidden;
        }

        .scene-card {
            background: rgba(255, 255, 255, 0.95);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .scene-active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .add-scene {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
        }
    </style>
</head>

<body class="h-screen">
    <!-- iOS状态栏 -->
    <div class="fixed top-0 left-0 right-0 text-black text-sm px-4 py-2 flex justify-between items-center z-50"
        style="background: linear-gradient(180deg, #e6f3ff 0%, #f0f8ff 100%);">
        <div class="font-semibold">
            9:41
        </div>
        <div class="flex items-center space-x-1">
            <i class="fas fa-signal text-xs"></i>
            <i class="fas fa-wifi text-xs"></i>
            <i class="fas fa-battery-full text-xs"></i>
        </div>
    </div>

    <!-- 主内容区域 -->
    <div class="flex-1 overflow-y-auto pt-12 pb-20">
        <!-- 顶部导航 -->
        <div class="flex justify-between items-center p-4">
            <div>
                <h1 class="text-2xl font-bold text-gray-800">场景</h1>
                <p class="text-sm text-gray-600">智能场景，一键控制</p>
            </div>
            <div class="flex space-x-4">
                <button class="p-2 bg-white bg-opacity-60 rounded-full">
                    <i class="fas fa-plus text-gray-600"></i>
                </button>
                <button class="p-2 bg-white bg-opacity-60 rounded-full">
                    <i class="fas fa-list text-gray-600"></i>
                </button>
            </div>
        </div>

        <!-- 场景标签导航 -->
        <div class="px-4 mb-4">
            <div class="flex space-x-8">
                <button class="text-blue-500 text-sm font-medium border-b-2 border-blue-500 pb-1">场景</button>
                <button class="text-gray-500 text-sm">自动化</button>
            </div>
        </div>
        <!-- 手动场景 -->
        <div class="mx-4 mb-6 mt-4">
            <div class="space-y-3">
                <!-- 回家模式 -->
                <div class="scene-card scene-active rounded-xl p-4 cursor-pointer">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div
                                class="w-12 h-12 rounded-xl flex items-center justify-center mr-4" style="background-color: #0ea5e9;">
                                <i class="fas fa-sign-in-alt text-white text-xl"></i>
                            </div>
                            <div>
                                <h4 class="font-semibold mb-1">回家模式</h4>
                                <p class="text-sm opacity-90">开启客厅灯光，调节空调温度</p>
                                <span class="text-xs opacity-75">运行中</span>
                            </div>
                        </div>
                        <div class="w-10 h-6 bg-white bg-opacity-30 rounded-full flex items-center">
                            <div class="w-4 h-4 bg-white rounded-full ml-auto mr-1"></div>
                        </div>
                    </div>
                </div>

                <!-- 睡眠模式 -->
                <div class="scene-card rounded-xl p-4 cursor-pointer">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-12 h-12 rounded-xl flex items-center justify-center mr-4" style="background-color: #8b5cf6;">
                                <i class="fas fa-moon text-white text-xl"></i>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-800 mb-1">睡眠模式</h4>
                                <p class="text-sm text-gray-600">关闭所有灯光，启动安防</p>
                                <span class="text-xs text-gray-500">未运行</span>
                            </div>
                        </div>
                        <div class="w-10 h-6 bg-gray-200 rounded-full flex items-center">
                            <div class="w-4 h-4 bg-white rounded-full ml-1"></div>
                        </div>
                    </div>
                </div>

                <!-- 离家模式 -->
                <div class="scene-card rounded-xl p-4 cursor-pointer">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-12 h-12 rounded-xl flex items-center justify-center mr-4" style="background-color: #f97316;">
                                <i class="fas fa-sign-out-alt text-white text-xl"></i>
                            </div>

                            <div>
                                <h4 class="font-semibold text-gray-800 mb-1">离家模式</h4>
                                <p class="text-sm text-gray-600">关闭电器，启动安防监控</p>
                                <span class="text-xs text-gray-500">未运行</span>
                            </div>
                        </div>
                        <div class="w-10 h-6 bg-gray-200 rounded-full flex items-center">
                            <div class="w-4 h-4 bg-white rounded-full ml-1"></div>
                        </div>
                    </div>
                </div>

                <!-- 添加场景 -->
                <div class="scene-card add-scene rounded-xl p-4 cursor-pointer">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-white bg-opacity-30 rounded-xl flex items-center justify-center mr-4">
                            <i class="fas fa-plus text-white text-xl"></i>
                        </div>
                        <div>
                            <h4 class="font-semibold mb-1">添加场景</h4>
                            <p class="text-sm opacity-90">创建自定义场景</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>





    </div>

    <!-- 底部导航栏 -->
    <div class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200">
        <div class="flex justify-around py-2">
            <button class="flex flex-col items-center py-2 px-3">
                <i class="fas fa-home text-gray-400 text-lg mb-1"></i>
                <span class="text-xs text-gray-400">首页</span>
            </button>
            <button class="flex flex-col items-center py-2 px-3">
                <i class="fas fa-layer-group text-blue-500 text-lg mb-1"></i>
                <span class="text-xs text-blue-500 font-medium">场景</span>
            </button>
            <button class="flex flex-col items-center py-2 px-3">
                <i class="fas fa-robot text-gray-400 text-lg mb-1"></i>
                <span class="text-xs text-gray-400">AI智能体</span>
            </button>
            <button class="flex flex-col items-center py-2 px-3">
                <i class="fas fa-store text-gray-400 text-lg mb-1"></i>
                <span class="text-xs text-gray-400">商城</span>
            </button>
            <button class="flex flex-col items-center py-2 px-3">
                <i class="fas fa-user text-gray-400 text-lg mb-1"></i>
                <span class="text-xs text-gray-400">我的</span>
            </button>
        </div>
    </div>

    <!-- iOS底部指示器 -->
    <div class="fixed bottom-0 left-0 right-0 flex justify-center pt-4 pb-2 bg-transparent">
        <div class="w-32 h-1 bg-black rounded-full"></div>
    </div>
</body>
</html>