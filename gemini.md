# 合觅App UE设计规范

## 📱 概述

本文档定义了合觅App UE原型设计的标准规范，确保所有页面保持一致的iOS原生体验。

## 🎯 核心原则

### 静态页面原则
- **严格禁止JavaScript**：所有页面必须是纯静态HTML+CSS
- **状态展示**：使用静态HTML结构展示不同状态
- **开关控件**：静态展示开启/关闭状态，无实际交互
- **无跳转链接**：使用div替代a标签

### iOS设计原则
- 基于iOS系统设计，遵守苹果设计惯例
- 保持视觉一致性和交互一致性
- 使用iOS标准组件和布局模式
- 支持iOS安全区域和状态栏适配

## 📐 布局结构

### 页面层级结构
```
iOS状态栏 (z-50, top-0)
├── 顶部导航栏 (z-40, top-10)
├── 告警横条 (z-30, top-20) [可选]
├── 主内容区域 (pt-24/pt-36)
├── 底部按钮 (bottom-8)
└── iOS底部指示器 (bottom-0)
```

### 标准间距
- **普通页面**: `pt-24 pb-20 px-4`
- **含告警页面**: `pt-36 pb-20 px-4`
- **布局适配**: 不同状态页面保持垂直对齐一致性

### iOS状态栏
```html
<div class="fixed top-0 left-0 right-0 text-black text-sm px-4 py-2 flex justify-between items-center z-50"
    style="background: linear-gradient(180deg, #e6f3ff 0%, #f0f8ff 100%);">
    <div class="font-semibold">9:41</div>
    <div class="flex items-center space-x-1">
        <i class="fas fa-signal text-xs"></i>
        <i class="fas fa-wifi text-xs"></i>
        <i class="fas fa-battery-full text-xs"></i>
    </div>
</div>
```

### 顶部导航栏
```html
<div class="fixed top-10 left-0 right-0 bg-transparent px-4 py-3 z-40">
    <div class="flex items-center justify-center relative">
        <button class="absolute left-0">
            <i class="fas fa-chevron-left text-gray-600 text-lg"></i>
        </button>
        <h1 class="text-lg font-semibold text-gray-800">页面标题</h1>
        <div class="absolute right-0">
            <i class="fas fa-ellipsis-h text-gray-600 text-lg"></i>
        </div>
    </div>
</div>
```

### 告警横条
```html
<div class="fixed top-20 left-0 right-0 z-30">
    <div class="low-battery-alert p-2 flex items-center justify-between">
        <div class="flex items-center">
            <div class="w-6 h-6 bg-white bg-opacity-20 rounded flex items-center justify-center mr-2">
                <i class="fas fa-battery-quarter text-white text-xs"></i>
            </div>
            <div class="text-white font-medium text-xs">电量不足 15%，请及时充电</div>
        </div>
        <button class="w-5 h-5 flex items-center justify-center">
            <i class="fas fa-times text-white text-xs"></i>
        </button>
    </div>
</div>
```

**告警横条规范**:
- 无圆角、无阴影、无边距，与页面宽度一致
- 紧凑高度设计 `p-2`，一行简洁文案
- 右侧关闭按钮，渐变背景

### iOS底部指示器
```html
<div class="fixed bottom-0 left-0 right-0 flex justify-center pt-4 pb-2 bg-transparent">
    <div class="w-32 h-1 bg-black rounded-full"></div>
</div>
```

## 🎨 组件规范

### 按钮组件
#### 主要按钮
```html
<div class="fixed bottom-8 left-0 right-0 bg-transparent px-4">
    <button class="w-full bg-blue-500 text-white py-4 rounded-xl font-medium text-base">
        按钮文字
    </button>
</div>
```

#### 次要按钮
- 透明背景或白色背景
- 蓝色边框和文字
- 相同的圆角和内边距

### 列表项组件
**适用**: device-item、action-item、scene-item、rotation-item

```css
.item-component {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    padding: 12px;
    margin: 6px 0;
    border: 2px solid transparent;
}
```

**统一规范**:
- 内边距: `padding: 12px`
- 外边距: `margin: 6px 0`
- 圆角: `border-radius: 12px`
- 图标容器: `w-8 h-8` (32px × 32px)
- 列表间距: `space-y-3` (12px)

### 操作提示组件
```html
<div class="mt-8 mb-4 px-2">
    <div class="flex items-start">
        <div class="flex-shrink-0 mr-3">
            <i class="fas fa-lightbulb text-blue-500 text-lg"></i>
        </div>
        <div class="flex-1">
            <p class="text-blue-600 font-medium text-sm mb-1">提示标题</p>
            <p class="text-gray-600 text-sm leading-relaxed">提示内容详情</p>
        </div>
    </div>
</div>
```

### 开关控件
#### 开启状态
```html
<div class="relative inline-block w-12 h-6">
    <div class="block w-full h-full bg-blue-500 rounded-full">
        <span class="absolute left-1 top-1 w-4 h-4 bg-white rounded-full transform translate-x-6"></span>
    </div>
</div>
```

#### 关闭状态
```html
<div class="relative inline-block w-12 h-6">
    <div class="block w-full h-full bg-gray-300 rounded-full">
        <span class="absolute left-1 top-1 w-4 h-4 bg-white rounded-full"></span>
    </div>
</div>
```

### 功能设置分组
```html
<!-- 功能设置区域 -->
<div class="mx-4 mt-4">
    <div class="mb-3">
        <h3 class="text-sm font-medium text-gray-500 px-2">功能设置</h3>
    </div>
    <div class="space-y-1">
        <!-- 功能相关设置项 -->
    </div>
</div>

<!-- 通用设置区域 -->
<div class="mx-4 mt-8">
    <div class="mb-3">
        <h3 class="text-sm font-medium text-gray-500 px-2">通用设置</h3>
    </div>
    <div class="space-y-1">
        <!-- 通用管理设置项 -->
    </div>
</div>
```

**分组原则**:
- **功能设置**: 与设备功能直接相关的配置
- **通用设置**: 设备基本信息和管理功能

## 🎨 设计规范

### 颜色规范
#### 主色调
- **主蓝色**: `#3b82f6` (bg-blue-500)
- **深蓝色**: `#1d4ed8` (bg-blue-700)
- **浅蓝色**: `#60a5fa` (bg-blue-400)

#### 文字颜色
- **主文字**: `#1f2937` (text-gray-800)
- **次要文字**: `#6b7280` (text-gray-500)
- **辅助文字**: `#9ca3af` (text-gray-400)
- **链接文字**: `#2563eb` (text-blue-600)

#### 背景颜色
- **页面背景**: `linear-gradient(180deg, #e6f3ff 0%, #f0f8ff 50%, #e8f4f8 100%)`
- **卡片背景**: `rgba(255, 255, 255, 0.9)` + `backdrop-filter: blur(10px)`
- **状态栏背景**: `linear-gradient(180deg, #e6f3ff 0%, #f0f8ff 100%)`

#### 状态颜色
- **成功**: `#10b981` (green-500)
- **警告**: `#f59e0b` (yellow-500)
- **错误**: `#ef4444` (red-500)
- **信息**: `#3b82f6` (blue-500)

### 字体规范
#### 字体大小
- **大标题**: `text-2xl` (24px)
- **页面标题**: `text-lg` (18px)
- **卡片标题**: `text-base` (16px)
- **正文**: `text-sm` (14px)
- **辅助文字**: `text-xs` (12px)

#### 字体粗细
- **粗体**: `font-bold`
- **半粗体**: `font-semibold`
- **中等**: `font-medium`
- **正常**: `font-normal`

### 间距规范
#### 标准间距
- **xs**: `0.25rem` (4px)
- **sm**: `0.5rem` (8px)
- **md**: `1rem` (16px)
- **lg**: `1.5rem` (24px)
- **xl**: `2rem` (32px)
- **2xl**: `3rem` (48px)

#### 页面布局间距
- **页面边距**: `px-4` (16px)
- **卡片间距**: `mb-4` (16px)
- **组件内边距**: `p-3` 或 `p-4`
- **按钮内边距**: `py-4 px-6`

## 🔧 特殊功能规范

### 极速模式设计
**禁用状态视觉规范**:
- 整体透明度: `opacity-50`
- 图标颜色: 灰色 `text-gray-400`
- 背景颜色: 灰色 `bg-gray-100`
- 右侧图标: 锁定图标 `fa-lock`
- 禁用文案: "极速模式下不可用"

### 导航栏规范
**右侧图标规则**:
- **三个点图标** (`fa-ellipsis-h`) - 表示"更多"功能菜单
- **设置图标** (`fa-cog`) - 不应放在导航栏右侧
- **保存按钮** - 使用文字"保存"，蓝色样式

### 页面布局优化
**一屏显示原则**:
- 页面内容应适配一屏显示，避免滚动
- 通过调整间距、字体大小、内容数量实现
- 优先级：功能完整性 > 视觉美观 > 内容丰富度

**紧凑布局技巧**:
- 减少 `margin` 和 `padding` 值
- 缩小图标和字体大小
- 减少非必要的内容选项
- 使用 `space-y-2` 替代 `space-y-3`

### 滚动页面底部按钮规范
**跟随内容原则**: 底部按钮应跟随页面内容移动，而非固定浮动

#### 正确的布局结构
```html
<!-- 主内容区域 -->
<div class="pt-24 pb-8">
    <!-- 内容区域1 -->
    <div class="mx-4 mt-4">
        <div class="setting-item rounded-xl p-4">
            <!-- 内容 -->
        </div>
    </div>

    <!-- 内容区域2 -->
    <div class="mx-4 mt-8">
        <div class="setting-item rounded-xl p-4">
            <!-- 内容 -->
        </div>
    </div>

    <!-- 底部按钮 -->
    <div class="mx-4 mt-12">
        <div class="w-full bg-blue-500 text-white py-4 rounded-xl font-medium text-base text-center">
            按钮文字
        </div>
    </div>
</div>
```

#### 关键规范要点
- **主容器间距**: `pt-24 pb-8` - 顶部间距适配导航栏，底部间距确保与iOS底部指示器32px间距
- **内容区域结构**: 每个内容区域使用 `mx-4` + `mt-*` 包装，内部使用具体的组件样式
- **按钮间距**: `mx-4 mt-12` - 与上方内容48px间距，无底部间距
- **按钮样式**: 使用 `div` 替代 `button`，添加 `text-center` 确保文字居中

#### 错误的做法
❌ **固定浮动**: `fixed bottom-8` - 按钮固定在屏幕底部
❌ **主容器有px-4**: `pt-24 pb-8 px-4` - 应该由子元素控制左右间距
❌ **内容区域有底部间距**: `mb-6` - 会破坏按钮与内容的间距关系
❌ **按钮有底部间距**: `mb-8` - 会与主容器的pb-8重复

#### 间距计算逻辑
- **与上方内容间距**: 由按钮的 `mt-12` (48px) 控制
- **与iOS底部指示器间距**: 由主容器的 `pb-8` (32px) 控制
- **左右边距**: 由各区域的 `mx-4` (16px) 统一控制

#### 参考实现
- **标准示例**: `UE/home/<USER>/41.html` (删除设备按钮)
- **应用示例**: `UE/home/<USER>/25.html` (完成按钮)

## 📁 文件组织规范

### 目录结构规则
**模块化组织原则**: UE目录下的HTML页面按功能模块组织，便于管理和交流

#### 文件命名规范
- **数字命名**: 除index.html和sub-index.html外，所有页面使用不重复的数字命名
- **模块分组**: 按功能模块创建子目录进行文件组织
- **全局唯一性**: 确保数字在整个UE目录中不重复，方便交流引用
- **重复解决**: 发现重复文件名时，保留核心功能页面编号，重命名其他重复文件

#### 目录结构示例
```
UE/
├── index.html                    # 主索引页面
├── home/                         # 首页模块
│   ├── 01.html                  # 首页主页面
│   ├── sub-index.html           # 模块索引页面
│   ├── gateway/                 # 5G CPE网关子模块
│   │   ├── 07.html              # 网关详情页
│   │   ├── 08.html              # 按型号添加
│   │   └── ...                  # 其他网关页面 (07-24)
│   ├── knob/                    # 智能旋钮子模块
│   │   ├── 25.html              # 选择子设备
│   │   ├── 26.html              # 无网关添加
│   │   └── ...                  # 其他旋钮页面 (25-45, 50-51)
│   └── light/                   # 人体存在平板灯子模块
│       ├── 46.html              # 设备详情
│       ├── 47.html              # 感应设置
│       └── ...                  # 其他灯光页面 (46-55)
├── scenes/                       # 场景模块
│   ├── 02.html                  # 场景主页面
│   └── sub-index.html           # 场景索引页面
├── ai-assistant/                 # AI助手模块
│   ├── 03.html                  # AI聊天界面
│   └── sub-index.html           # AI索引页面
├── mall/                         # 商城模块
│   ├── 04.html                  # 产品目录
│   └── sub-index.html           # 商城索引页面
└── profile/                      # 个人中心模块
    ├── 05.html                  # 用户设置主页面
    ├── 49.html                  # 我的订单
    ├── 52.html                  # 声纹管理
    └── sub-index.html           # 个人中心索引页面
```

#### 数字分配规则
- **主导航页面**: 01-05.html (首页、场景、AI助手、商城、我的)
- **网关模块**: 07-24.html (18个页面)
- **旋钮模块**: 25-45.html, 50-51.html (23个页面)
- **灯光模块**: 46-55.html (10个页面)
- **个人中心模块**: 49.html, 52.html (分散编号)
- **预留空间**: 56-105.html (为未来功能扩展预留)

#### 交流便利性
- **简洁引用**: 可直接说"46页面"而非完整路径
- **快速定位**: 通过数字快速找到对应页面文件
- **版本管理**: 数字命名便于版本控制和文件追踪

### 重复文件解决规范
**全局唯一性原则**: 确保所有HTML文件名在整个UE目录中全局唯一

#### 重复检测方法
```bash
# 检测重复文件名
find UE -name "*.html" -not -path "*/sub-index.html" -exec basename {} \; | sort | uniq -d
```

#### 重复解决策略
1. **保留核心功能**: 保留主要功能模块的原始编号
2. **重命名次要功能**: 为重复文件分配新的唯一编号
3. **同步更新链接**: 更新所有相关的sub-index.html文件中的链接
4. **文档记录**: 记录重命名映射关系，便于追踪

#### 重命名优先级
- **灯光模块**: 优先保留（核心设备功能）
- **旋钮模块**: 次优先保留（主要交互功能）
- **个人中心模块**: 可重命名（辅助功能）
- **其他模块**: 根据功能重要性决定

## 🔧 技术实现

### HTML结构完整性规范
**核心原则**: HTML结构完整性是布局正确的第一优先级

#### 常见结构错误
```html
<!-- ❌ 错误：主容器被意外关闭 -->
<div class="pt-24 pb-8 px-4"></div>  <!-- 容器错误关闭 -->
<div class="action-card">内容</div>   <!-- 内容跑到容器外 -->
<div class="w-full bg-blue-500">按钮</div>  <!-- 按钮失去pb-8间距 -->
</div>  <!-- 多余的关闭标签 -->
```

```html
<!-- ✅ 正确：主容器完整包含所有内容 -->
<div class="pt-24 pb-8 px-4">  <!-- 容器正确包含所有内容 -->
    <div class="action-card">内容</div>
    <div class="w-full bg-blue-500">按钮</div>  <!-- 按钮享受pb-8间距 -->
</div>  <!-- 容器正确关闭 -->
```

#### 间距计算逻辑
**底部按钮间距计算**:
- 按钮与iOS底部指示器的视觉间距 = 主容器`pb-8`(32px) + 指示器`pt-4`(16px) = 48px
- 按钮与上方内容间距 = 按钮`mt-12`(48px)
- 左右边距 = 主容器`px-4`(16px)

#### 调试检查清单
- [ ] 主容器`<div class="pt-24 pb-8 px-4">`是否正确包含所有内容
- [ ] 按钮是否在主容器内部（享受pb-8间距）
- [ ] HTML标签是否正确配对（无多余或缺失的关闭标签）
- [ ] DOM结构是否与参考页面一致

#### 结构修复方法
1. **参考标准页面**: 以27页为模板，严格复制其HTML结构
2. **逐步验证**: 先确保基础结构正确，再添加具体内容
3. **容器优先**: 确保主容器完整性，再处理内部元素
4. **工具验证**: 使用浏览器开发者工具检查DOM结构

### 图标背景修复
**问题**: Tailwind CSS背景色可能被其他样式覆盖导致不可见

**解决方案**: 使用内联样式确保背景显示
```html
<!-- ❌ 可能被覆盖 -->
<div class="w-12 h-12 bg-orange-500 rounded-lg">
    <i class="fas fa-adjust text-white"></i>
</div>

<!-- ✅ 使用内联样式 -->
<div class="w-12 h-12 rounded-lg" style="background: linear-gradient(135deg, #f97316 0%, #ea580c 100%);">
    <i class="fas fa-adjust text-white"></i>
</div>
```

### 响应式设计
- **iPhone标准**: 393px × 852px
- **安全区域**: 考虑刘海屏和底部安全区域
- **最小触摸区域**: 44px × 44px

## ✅ 检查清单

### 基础规范
- [ ] iOS状态栏样式正确
- [ ] 顶部导航栏布局标准（左返回、中标题、右更多）
- [ ] 主内容区域间距为 pt-24/pt-36 pb-20
- [ ] iOS底部指示器正确显示
- [ ] 颜色、字体使用符合规范

### 组件规范
- [ ] 列表项组件统一高度（padding: 12px, margin: 6px 0）
- [ ] 图标容器统一使用 w-8 h-8 (32px × 32px)
- [ ] 按钮使用标准样式和位置
- [ ] 开关控件使用静态HTML展示不同状态

### 静态页面规范
- [ ] 页面完全静态，严格禁止JavaScript代码
- [ ] 无跳转链接，使用div替代a标签
- [ ] 操作提示使用蓝色灯泡图标

### 布局优化
- [ ] 页面内容适配一屏显示，避免滚动
- [ ] 告警横条无圆角、无阴影、全宽度
- [ ] 不同状态页面保持垂直对齐一致性
- [ ] 功能设置使用分组设计（功能设置 vs 通用设置）

## 📚 参考页面

### 核心规范参考
- **标准页面**: `UE/scenes/02.html` (场景管理页面)
- **设备详情**: `UE/home/<USER>/07.html` (网关详情页)
- **导航栏规范**: `UE/home/<USER>/40.html` (智能旋钮详情页)
- **一屏布局**: `UE/home/<USER>/34.html` (选择按键页面)

### 组件规范参考
- **列表项组件**: `UE/home/<USER>/35.html` (操作配置页面)
- **静态页面**: `UE/home/<USER>/31.html` (旋转动作设置)
- **操作提示**: `UE/home/<USER>/15.html` (发现子设备)

### 特殊功能参考
- **告警横条**: `UE/home/<USER>/55.html` (更多设置页面)
- **功能分组**: `UE/home/<USER>/55.html` (更多设置页面)
- **疾速模式**: `UE/home/<USER>/33.html` (选择按键-疾速模式)
- **静态开关**: `UE/home/<USER>/50.html` (感应设置页面)
- **图标修复**: `UE/home/<USER>/55.html` (更多设置页面)
- **定时功能**: `UE/home/<USER>/54.html` (定时列表), `UE/home/<USER>/55.html` (新增定时)

### 配置流程参考
- **按键配置**: `UE/home/<USER>/35.html` (选择设备页面)
- **配置结果**: `UE/home/<USER>/36.html` (配置结果页面)
- **疾速配置**: `UE/home/<USER>/50.html` (疾速模式配置结果)

## 🎯 新增设计规范 (2025-07-18)

### 术语统一规范
**核心原则**: 保持产品术语的一致性和专业性

#### 模式命名
- **疾速模式**: 替代"极速模式"，更优雅和专业
- **基础设置**: 替代"快速设置"，与"高级设置"形成层次对比
- **高级设置**: 替代"高级玩法"，更正式和专业

#### 操作术语
- **逆时针旋转**: 替代"左旋"，更准确和标准
- **顺时针旋转**: 替代"右旋"，更准确和标准
- **已选设备**: 替代"已选择设备"，更简洁

### 页面标题规范
**功能导向原则**: 页面标题应准确反映当前配置的具体功能

#### 标题命名规则
- **具体功能**: "顺时针旋转" 而非 "上键配置"
- **状态标识**: "选择按键（疾速模式开启）" 明确模式状态
- **功能分层**: "配置结果（疾速模式开启）" 区分不同模式

### 模式标识规范
**简洁原则**: 避免冗余的模式标识显示

#### 标识使用规则
- **页面内标识**: 删除页面内的模式标识胶囊，保持界面简洁
- **索引页标识**: 在索引页面标题中体现模式状态
- **功能说明**: 通过功能描述体现模式特性，而非显式标识

### 内容一致性规范
**高度统一原则**: 相同类型的页面应保持完全一致的布局高度

#### 配置页面规范
- **当前配置显示**: 统一使用 `mb-4` 底部间距
- **功能说明区域**: 保持相同的内边距和布局结构
- **按钮区域**: 统一的位置和样式

### 设备信息简化规范
**信息聚焦原则**: 只显示最核心的设备信息

#### 设备发现页面
- **删除时间备注**: 去掉"刚刚发现"、"30秒前发现"等时间信息
- **删除状态备注**: 去掉"已添加"等状态信息
- **保留核心信息**: 只保留设备名称和DN码

#### 设备配置页面
- **设备描述**: 使用具体设备名称，如"卧室窗帘"
- **功能描述**: 使用具体操作，如"窗帘打开"而非"亮度调亮"

### 功能专注规范
**单一职责原则**: 每个页面专注于一个核心功能

#### 功能说明简化
- **删除无关功能**: 只保留与当前配置相关的功能说明
- **突出主功能**: 将主要功能放在显眼位置
- **统一描述**: 使用一致的功能描述语言

### iOS状态栏一致性规范
**视觉统一原则**: 所有页面的iOS状态栏必须完全一致

#### 状态栏标准
- **电池图标**: 统一使用 `fa-battery-full text-xs` (满电量黑色)
- **避免特殊状态**: 不在状态栏中显示低电量等特殊状态
- **保持标准**: 时间显示"9:41"，信号、WiFi、电池图标标准化

### 页面隐藏管理规范
**功能控制原则**: 通过CSS控制功能模块的显示/隐藏

#### 隐藏方式
- **CSS隐藏**: 使用 `style="display: none;"` 隐藏整个功能模块
- **保留代码**: 隐藏的功能代码保留，便于后续启用
- **灵活控制**: 可随时通过移除隐藏样式重新启用功能

---

*最后更新: 2025-07-22*
*版本: v1.7 - 滚动页面底部按钮规范*

## 📝 更新日志

### v1.7 (2025-07-22)
- **新增**: 滚动页面底部按钮规范，解决按钮布局和间距问题
- **规范**: 明确主容器结构、内容区域组织和按钮间距计算逻辑
- **示例**: 提供标准实现参考和错误做法对比
- **完善**: 补充布局结构代码示例和间距计算说明

### v1.6 (2025-07-22)
- **重构**: 全局文件编号系统，主导航页面改为01-05编号
- **解决**: 重复文件名问题，确保全局唯一性
- **新增**: 重复文件解决规范，包含检测方法和解决策略
- **更新**: 所有参考页面链接，匹配重构后的文件编号
- **优化**: 数字分配规则，明确各模块的编号范围和预留空间

### v1.5 (2025-07-22)
- **新增**: 文件组织规范，明确模块化目录结构和数字命名规则
- **更新**: 参考页面链接，匹配当前文件结构
- **修正**: 术语统一，将"极速模式"改为"疾速模式"
- **完善**: 数字分配规则，明确各模块的数字区间

### v1.4 (2025-07-18)
- **新增**: 术语统一、页面标题、模式标识等8项新设计规范
- **优化**: 基于实际开发经验总结的设计原则
- **完善**: 补充iOS状态栏一致性和功能隐藏管理规范

### v1.3 (2025-07-17)
- **重构**: 全文重构，消除重复内容
- **优化**: 重新组织文档结构
- **简化**: 合并相似规范，提高可读性
- **精简**: 减少冗余描述，突出核心要点

### v1.2 (2025-07-17)
- 新增告警横条、功能分组、极速模式等设计规范

### v1.1 (2025-07-14)
- 初始版本发布
